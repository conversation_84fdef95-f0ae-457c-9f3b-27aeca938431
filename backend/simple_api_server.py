#!/usr/bin/env python3
"""
Jednoduchý API server pro demonstraci autonomní architektury
Bez závislostí na crewai - používá mock orchestrátor
"""

import uvicorn
import logging
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
import uuid
from datetime import datetime

# Import skutečného orchestrátoru
from autonomous_orchestrator import AutonomousOrchestrator

# Logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="Matylda v2.0 - Autonomní API",
    description="Autonomní architektura s reálnými AI modely a nástroji",
    version="2.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic modely
class ChatRequest(BaseModel):
    message: str = Field(..., description="Zpráva od uživatele")
    session_id: Optional[str] = Field(None, description="ID session pro pokračování konverzace")
    domain: Optional[str] = Field(None, description="Explicitní doména")
    request_type: Optional[str] = Field(None, description="Typ požadavku")

class ChatResponse(BaseModel):
    session_id: str
    chat_response: Optional[str] = None
    canvas_content: Optional[str] = None
    question: Optional[str] = None  # Zpětná kompatibilita
    message: Optional[str] = None
    final_analysis: Optional[str] = None
    status: str = "active"
    is_complete: bool = False
    completion_percentage: int = 0
    domain: Optional[str] = None
    domain_name: Optional[str] = None

# Globální instance orchestrátoru
autonomous_orchestrator = AutonomousOrchestrator()

# Session storage (v produkci by bylo v databázi)
active_sessions: Dict[str, Dict[str, Any]] = {}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "message": "Matylda v2.0 Autonomní API je aktivní",
        "version": "2.0.0",
        "timestamp": datetime.now().isoformat(),
        "architecture": "autonomous"
    }

@app.post("/chat/universal", response_model=ChatResponse)
async def universal_chat(request: ChatRequest):
    """
    Univerzální chat endpoint používající autonomní architekturu.
    Jeden super-agent řeší celý problém dynamicky.
    """
    try:
        logger.info(f"🚀 Autonomní požadavek: {request.message[:50]}...")

        if not request.session_id:
            # Nová konverzace - autonomní zpracování
            logger.info(f"🆕 Nová autonomní konverzace")

            result = autonomous_orchestrator.process_request(
                initial_request=request.message,
                domain=request.domain,
                request_type=request.request_type
            )

            # Uložení session
            session_id = result.get("session_id")
            active_sessions[session_id] = {
                "initial_request": request.message,
                "history": [{"role": "user", "content": request.message}],
                "domain": result.get("domain"),
                "created_at": datetime.now().isoformat()
            }

            return ChatResponse(
                session_id=session_id,
                chat_response=result.get("chat_response"),
                canvas_content=result.get("canvas_content"),
                question=result.get("chat_response"),  # Zpětná kompatibilita
                status=result.get("status", "active"),
                is_complete=result.get("is_complete", False),
                completion_percentage=result.get("completion_percentage", 0),
                domain=result.get("domain"),
                domain_name=result.get("domain_name")
            )
        else:
            # Pokračování konverzace
            logger.info(f"🔄 Pokračování autonomní konverzace {request.session_id}")

            if request.session_id not in active_sessions:
                raise HTTPException(status_code=404, detail="Session nenalezena")

            session = active_sessions[request.session_id]
            session["history"].append({"role": "user", "content": request.message})

            # Pro pokračování konverzace použijeme stejnou logiku
            result = autonomous_orchestrator.process_request(
                initial_request=f"Pokračování: {request.message}",
                domain=session.get("domain"),
                context=session["history"]
            )

            session["history"].append({"role": "assistant", "content": result.get("chat_response")})

            return ChatResponse(
                session_id=request.session_id,
                chat_response=result.get("chat_response"),
                canvas_content=result.get("canvas_content"),
                question=result.get("chat_response"),  # Zpětná kompatibilita
                status=result.get("status", "active"),
                is_complete=result.get("is_complete", False),
                completion_percentage=result.get("completion_percentage", 0),
                domain=result.get("domain"),
                domain_name=result.get("domain_name")
            )

    except Exception as e:
        logger.error(f"❌ Chyba v autonomním chat endpointu: {e}")
        return ChatResponse(
            session_id=request.session_id or f"error-{uuid.uuid4().hex[:8]}",
            chat_response=f"Omlouvám se, došlo k chybě: {str(e)}",
            canvas_content="# ❌ Chyba\n\nDošlo k chybě při zpracování.",
            status="error",
            is_complete=False,
            completion_percentage=0,
            domain="error",
            domain_name="Chyba"
        )

@app.get("/sessions")
async def list_sessions():
    """Seznam aktivních sessions pro debugging"""
    return {
        "active_sessions": len(active_sessions),
        "sessions": {
            session_id: {
                "domain": session.get("domain"),
                "created_at": session.get("created_at"),
                "messages": len(session.get("history", []))
            }
            for session_id, session in active_sessions.items()
        }
    }

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Matylda v2.0 - Autonomní Architektura",
        "description": "Jeden super-agent pro dynamické řešení komplexních požadavků",
        "version": "2.0.0",
        "endpoints": {
            "health": "/health",
            "chat": "/chat/universal",
            "sessions": "/sessions",
            "docs": "/docs"
        }
    }


# ============================================================================
# CANVAS API ENDPOINTS
# ============================================================================

@app.get("/canvas/{session_id}")
async def get_canvas_views(session_id: str, debug: bool = False):
    """
    Vrátí canvas views pro session (mock implementace)
    """
    try:
        # Základní client_view (vždy)
        client_view = f"""# 📋 Test Projekt {session_id[:8]}

*Poslední aktualizace: {datetime.now().strftime("%Y-%m-%d %H:%M")}*
*Status: 🧪 Testování*

## 🎯 Co umím:

- ✅ Analýza výzkumných požadavků a strategické plánování
- ✅ Návrh metodologie pro průzkumy veřejného mínění
- ✅ Specializace na městskou problematiku a sociologii
- ✅ Tvorba dotazníků s pokročilými technikami (Likertova škála, sémantický diferenciál)
- ✅ Export do Limesurvey formátu (.lss)
- ✅ Zajištění GDPR compliance
- ✅ Vizualizace projektového postupu

## Specializace:

- Průzkumy společenských občanů v městských částech
- Analýza dopravních návyků a potřeb
- Hodnocení veřejných služeb a infrastruktury
- Výzkum bezpečnosti a životního prostředí
- Demografické a urbanistické analýzy"""

        if debug:
            debug_view = f"""# 🔧 DEBUG VIEW - {session_id[:8]}

## 📊 Metadata
- **Project ID:** {session_id}
- **Created:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- **Current Phase:** testing
- **Total Versions:** 1

## 📋 Sections Detail

### 🎯 Přehled Projektu (`project_overview`)
- **Owner:** test_agent
- **Status:** draft
- **Version:** 1
- **Last Updated:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

**Content:**
```
Testovací projekt pro ověření canvas funkcionalita.
```

**Agent Reasoning:**
```
Testujeme základní funkcionalita sdíleného plátna.
```

---

## 📚 Version History

- **v1** (test_agent) - Inicializace projektu - {datetime.now().strftime('%H:%M:%S')}
"""

            return {
                "session_id": session_id,
                "client_view": client_view,
                "debug_view": debug_view,
                "current_activities": [],
                "activity_history": [],
                "metadata": {
                    "project_name": f"Test Projekt {session_id[:8]}",
                    "current_phase": "testing",
                    "last_updated": datetime.now().isoformat(),
                    "total_versions": 1
                }
            }
        else:
            return {
                "session_id": session_id,
                "client_view": client_view,
                "metadata": {
                    "project_name": f"Test Projekt {session_id[:8]}",
                    "current_phase": "testing",
                    "last_updated": datetime.now().isoformat(),
                    "total_versions": 1
                }
            }

    except Exception as e:
        logger.error(f"❌ Chyba při získávání canvas views: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/canvas/{session_id}/activities")
async def get_agent_activities(session_id: str):
    """
    Vrátí aktuální aktivity agentů pro session (mock implementace)
    """
    try:
        mock_activities = [
            {
                "agent_id": "canvas_manager",
                "activity_type": "updating",
                "description": "Aktualizuji projektové plátno",
                "status": "completed",
                "timestamp": datetime.now().isoformat(),
                "duration": 0.5
            },
            {
                "agent_id": "websocket_client",
                "activity_type": "thinking",
                "description": "Navazuji WebSocket spojení",
                "status": "completed",
                "timestamp": datetime.now().isoformat(),
                "duration": 0.2
            }
        ]

        return {
            "session_id": session_id,
            "activities": mock_activities,
            "count": len(mock_activities)
        }

    except Exception as e:
        logger.error(f"❌ Chyba při získávání agent activities: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/canvas/{session_id}/debug")
async def get_debug_canvas(session_id: str):
    """
    Vrátí debug pohled na canvas (mock implementace)
    """
    try:
        debug_view = f"""# 🔧 DEBUG CANVAS - {session_id[:8]}

## System Info
- Session ID: {session_id}
- Server: simple_api_server.py
- Timestamp: {datetime.now().isoformat()}
- WebSocket: Active

## Mock Data
- Canvas views: Generated
- Activities: Mock data
- Real-time: WebSocket enabled

## Test Status
✅ WebSocket connection
✅ Canvas API endpoints
✅ Debug/Production toggle
⏳ Full integration testing
"""

        return {
            "session_id": session_id,
            "debug_view": debug_view
        }

    except Exception as e:
        logger.error(f"❌ Chyba při získávání debug canvas: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# ============================================================================
# WEBSOCKET SUPPORT
# ============================================================================

class SimpleWebSocketManager:
    """Jednoduchý WebSocket manager pro basic funkcionalitu"""

    def __init__(self):
        self.active_connections: Dict[str, list] = {}

    async def connect(self, websocket: WebSocket, session_id: str):
        await websocket.accept()
        if session_id not in self.active_connections:
            self.active_connections[session_id] = []
        self.active_connections[session_id].append(websocket)
        logger.info(f"🔌 WebSocket připojen pro session: {session_id}")

    def disconnect(self, websocket: WebSocket, session_id: str):
        if session_id in self.active_connections:
            if websocket in self.active_connections[session_id]:
                self.active_connections[session_id].remove(websocket)
            if not self.active_connections[session_id]:
                del self.active_connections[session_id]
        logger.info(f"🔌 WebSocket odpojen pro session: {session_id}")

# Globální WebSocket manager
ws_manager = SimpleWebSocketManager()

@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """Basic WebSocket endpoint pro testování"""
    await ws_manager.connect(websocket, session_id)

    try:
        # Úvodní zpráva
        await websocket.send_json({
            "type": "connection_established",
            "session_id": session_id,
            "message": "WebSocket připojení navázáno"
        })

        # Mock canvas update
        await websocket.send_json({
            "type": "canvas_update",
            "session_id": session_id,
            "canvas_views": {
                "client_view": "# Mock Canvas\nToto je testovací canvas obsah.",
                "metadata": {
                    "project_name": f"Projekt {session_id[:8]}",
                    "current_phase": "testing",
                    "last_updated": datetime.now().isoformat()
                }
            },
            "activities": [],
            "timestamp": datetime.now().isoformat()
        })

        # Keep-alive loop
        while True:
            try:
                data = await websocket.receive_json()

                if data.get("type") == "ping":
                    await websocket.send_json({
                        "type": "pong",
                        "timestamp": datetime.now().isoformat()
                    })
                elif data.get("type") == "request_update":
                    await websocket.send_json({
                        "type": "canvas_update",
                        "session_id": session_id,
                        "canvas_views": {
                            "client_view": f"# Updated Canvas\nAktualizováno: {datetime.now().strftime('%H:%M:%S')}",
                            "metadata": {
                                "project_name": f"Projekt {session_id[:8]}",
                                "current_phase": "testing",
                                "last_updated": datetime.now().isoformat()
                            }
                        },
                        "activities": [
                            {
                                "agent_id": "test_agent",
                                "activity_type": "testing",
                                "description": "Testování WebSocket komunikace",
                                "status": "completed",
                                "timestamp": datetime.now().isoformat()
                            }
                        ],
                        "timestamp": datetime.now().isoformat()
                    })

            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"❌ WebSocket chyba: {e}")
                break

    except WebSocketDisconnect:
        pass
    finally:
        ws_manager.disconnect(websocket, session_id)


if __name__ == "__main__":
    logger.info("🚀 Spouštím Matylda v2.0 Autonomní API Server...")
    uvicorn.run(
        "simple_api_server:app",
        host="0.0.0.0",
        port=8001,
        reload=False,
        log_level="info"
    )
