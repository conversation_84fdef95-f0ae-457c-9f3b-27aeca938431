#!/usr/bin/env python3
"""
Shared Project Canvas - Sdílené projektové plátno pro agenty
Implementuje "Single Source of Truth" s dvoúrovňovou transparentností
"""

import json
import uuid
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from enum import Enum
from pydantic import BaseModel, Field
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

# ============================================================================
# ENUMS A KONSTANTY
# ============================================================================

class ProjectPhase(Enum):
    """Fáze projektu"""
    SCOPING = "scoping"          # Fáze 1: Slaďování zadání
    RESEARCH = "research"        # Fáze 2: <PERSON><PERSON><PERSON><PERSON><PERSON> rešerše  
    DESIGN = "design"           # Fáze 3: Návrh řešení
    APPROVAL = "approval"       # Fáze 4: Schvalování
    DELIVERY = "delivery"       # Fáze 5: Dodání

class SectionStatus(Enum):
    """Stav sekce plátna"""
    DRAFT = "draft"              # ⏳ Rozpracováno
    REVIEW = "review"            # 🔄 K posouzení
    APPROVED = "approved"        # ✅ Schváleno
    FINAL = "final"              # 🏁 Finální
    NEEDS_UPDATE = "needs_update" # ❓ Vyžaduje aktualizaci

class ActivityType(Enum):
    """Typ aktivity agenta"""
    THINKING = "thinking"        # 🧠 Přemýšlí
    RESEARCHING = "researching"  # 🔍 Vyhledává
    WRITING = "writing"         # ✍️ Píše
    UPDATING = "updating"       # 🔄 Aktualizuje
    REVIEWING = "reviewing"     # 👀 Kontroluje
    WAITING = "waiting"         # ⏳ Čeká

class ActivityStatus(Enum):
    """Stav aktivity"""
    STARTED = "started"         # 🚀 Spuštěno
    IN_PROGRESS = "in_progress" # ⏳ Probíhá
    COMPLETED = "completed"     # ✅ Dokončeno
    FAILED = "failed"          # ❌ Selhalo
    PAUSED = "paused"          # ⏸️ Pozastaveno

# ============================================================================
# DATOVÉ STRUKTURY
# ============================================================================

@dataclass
class AgentActivity:
    """Aktivita agenta pro real-time tracking"""
    agent_id: str
    activity_type: ActivityType
    description: str
    status: ActivityStatus
    timestamp: datetime
    duration: Optional[float] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

@dataclass
class CanvasSection:
    """Sekce projektového plátna"""
    section_id: str
    title: str
    content: str
    owner_agent: str
    status: SectionStatus
    last_updated: datetime
    version: int = 1
    dependencies: List[str] = None
    reasoning: Optional[str] = None  # Interní reasoning agenta
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []

@dataclass
class CanvasVersion:
    """Verze plátna pro rollback"""
    version_id: str
    timestamp: datetime
    agent_id: str
    description: str
    sections_snapshot: Dict[str, CanvasSection]

class CanvasMetadata(BaseModel):
    """Metadata projektového plátna"""
    project_id: str
    project_name: str
    session_id: str
    created_at: datetime
    last_updated: datetime
    current_phase: ProjectPhase
    total_versions: int = 0
    
# ============================================================================
# STANDARDNÍ SEKCE PRO VÝZKUMNÉ PROJEKTY
# ============================================================================

STANDARD_SECTIONS = {
    "project_overview": "🎯 Přehled Projektu",
    "objectives": "📋 Cíle a Rozsah", 
    "methodology": "🔬 Metodologie",
    "timeline": "🗓️ Časový Harmonogram",
    "deliverables": "📦 Výstupy",
    "technical_specs": "⚙️ Technické Specifikace",
    "recommendations": "💡 Doporučení",
    "next_steps": "➡️ Další Kroky"
}

# ============================================================================
# AGENT ACTIVITY TRACKER
# ============================================================================

class AgentActivityTracker:
    """
    Sledování real-time aktivit agentů podobné ChatGPT reasoning módu
    """
    
    def __init__(self):
        self.activities: List[AgentActivity] = []
        self.lock = threading.Lock()
        self.max_activities = 100  # Limit pro paměť
        
    def log_activity(self, agent: str, activity: str, status: ActivityStatus, 
                    activity_type: ActivityType = ActivityType.THINKING,
                    metadata: Dict[str, Any] = None) -> str:
        """Zaloguje aktivitu agenta"""
        with self.lock:
            activity_obj = AgentActivity(
                agent_id=agent,
                activity_type=activity_type,
                description=activity,
                status=status,
                timestamp=datetime.now(),
                metadata=metadata or {}
            )
            
            self.activities.append(activity_obj)
            
            # Omezení počtu aktivit
            if len(self.activities) > self.max_activities:
                self.activities = self.activities[-self.max_activities:]
            
            logger.info(f"🤖 {agent}: {activity} ({status.value})")
            return f"{agent}_{len(self.activities)}"
    
    def update_activity_status(self, agent: str, activity_description: str, 
                              new_status: ActivityStatus, duration: float = None):
        """Aktualizuje stav aktivity"""
        with self.lock:
            for activity in reversed(self.activities):
                if (activity.agent_id == agent and 
                    activity.description == activity_description):
                    activity.status = new_status
                    if duration:
                        activity.duration = duration
                    break
    
    def get_current_activities(self, agent_id: str = None) -> List[AgentActivity]:
        """Vrátí aktuální aktivity (ne dokončené)"""
        with self.lock:
            activities = [a for a in self.activities 
                         if a.status in [ActivityStatus.STARTED, ActivityStatus.IN_PROGRESS]]
            
            if agent_id:
                activities = [a for a in activities if a.agent_id == agent_id]
                
            return activities
    
    def get_activity_history(self, agent_id: str = None, limit: int = 20) -> List[AgentActivity]:
        """Vrátí historii aktivit"""
        with self.lock:
            activities = self.activities[-limit:] if limit else self.activities
            
            if agent_id:
                activities = [a for a in activities if a.agent_id == agent_id]
                
            return activities
    
    def clear_completed_activities(self):
        """Vyčistí dokončené aktivity"""
        with self.lock:
            self.activities = [a for a in self.activities 
                             if a.status not in [ActivityStatus.COMPLETED, ActivityStatus.FAILED]]

# ============================================================================
# SHARED PROJECT CANVAS (CORE)
# ============================================================================

class SharedProjectCanvas:
    """
    Centrální úložiště stavu projektu s verzováním a multi-view podporou
    """
    
    def __init__(self, project_name: str = "Nový Projekt", session_id: str = None):
        self.metadata = CanvasMetadata(
            project_id=str(uuid.uuid4()),
            project_name=project_name,
            session_id=session_id or str(uuid.uuid4()),
            created_at=datetime.now(),
            last_updated=datetime.now(),
            current_phase=ProjectPhase.SCOPING
        )
        
        self.sections: Dict[str, CanvasSection] = {}
        self.version_history: List[CanvasVersion] = []
        self.lock = threading.Lock()
        
        # Inicializace standardních sekcí
        self._initialize_standard_sections()
        
        logger.info(f"🎨 SharedProjectCanvas inicializováno: {project_name}")
    
    def _initialize_standard_sections(self):
        """Inicializuje standardní sekce pro výzkumný projekt"""
        for section_id, title in STANDARD_SECTIONS.items():
            self.sections[section_id] = CanvasSection(
                section_id=section_id,
                title=title,
                content="⏳ Čeká na zpracování",
                owner_agent="system",
                status=SectionStatus.DRAFT,
                last_updated=datetime.now()
            )
    
    def update_by_agent(self, agent_id: str, section: str, content: str, 
                       reasoning: str = None, status: SectionStatus = SectionStatus.DRAFT) -> bool:
        """Agent aktualizuje sekci plátna"""
        with self.lock:
            try:
                # Vytvoření snapshot před změnou
                self._create_version_snapshot(agent_id, f"Aktualizace sekce {section}")
                
                # Aktualizace nebo vytvoření sekce
                if section in self.sections:
                    self.sections[section].content = content
                    self.sections[section].owner_agent = agent_id
                    self.sections[section].status = status
                    self.sections[section].last_updated = datetime.now()
                    self.sections[section].version += 1
                    if reasoning:
                        self.sections[section].reasoning = reasoning
                else:
                    # Nová sekce
                    title = STANDARD_SECTIONS.get(section, section.replace("_", " ").title())
                    self.sections[section] = CanvasSection(
                        section_id=section,
                        title=title,
                        content=content,
                        owner_agent=agent_id,
                        status=status,
                        last_updated=datetime.now(),
                        reasoning=reasoning
                    )
                
                # Aktualizace metadata
                self.metadata.last_updated = datetime.now()
                
                logger.info(f"✅ {agent_id} aktualizoval sekci '{section}'")
                return True
                
            except Exception as e:
                logger.error(f"❌ Chyba při aktualizaci sekce {section}: {e}")
                return False
    
    def _create_version_snapshot(self, agent_id: str, description: str) -> str:
        """Vytvoří snapshot aktuálního stavu"""
        version_id = f"v{self.metadata.total_versions + 1}_{int(datetime.now().timestamp())}"
        
        snapshot = CanvasVersion(
            version_id=version_id,
            timestamp=datetime.now(),
            agent_id=agent_id,
            description=description,
            sections_snapshot=self.sections.copy()
        )
        
        self.version_history.append(snapshot)
        self.metadata.total_versions += 1
        
        # Omezení historie (posledních 10 verzí)
        if len(self.version_history) > 10:
            self.version_history = self.version_history[-10:]
        
        return version_id

    def get_client_view(self) -> str:
        """Vrátí čistý Markdown pro klienta (bez debug informací)"""
        with self.lock:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
            phase_emoji = {
                ProjectPhase.SCOPING: "🔍",
                ProjectPhase.RESEARCH: "🔬",
                ProjectPhase.DESIGN: "🎨",
                ProjectPhase.APPROVAL: "✅",
                ProjectPhase.DELIVERY: "🚀"
            }

            markdown = f"""# 📋 {self.metadata.project_name}

*Poslední aktualizace: {current_time}*
*Status: {phase_emoji.get(self.metadata.current_phase, '🔄')} {self.metadata.current_phase.value.title()}*

---

"""

            # Generování sekcí v logickém pořadí
            section_order = [
                "project_overview", "objectives", "methodology",
                "timeline", "deliverables", "technical_specs",
                "recommendations", "next_steps"
            ]

            for section_id in section_order:
                if section_id in self.sections:
                    section = self.sections[section_id]
                    status_emoji = {
                        SectionStatus.DRAFT: "⏳",
                        SectionStatus.REVIEW: "🔄",
                        SectionStatus.APPROVED: "✅",
                        SectionStatus.FINAL: "🏁",
                        SectionStatus.NEEDS_UPDATE: "❓"
                    }

                    emoji = status_emoji.get(section.status, "⏳")
                    markdown += f"## {emoji} {section.title}\n\n"
                    markdown += f"{section.content}\n\n---\n\n"

            # Přidání dalších sekcí (ne-standardních)
            for section_id, section in self.sections.items():
                if section_id not in section_order:
                    emoji = status_emoji.get(section.status, "⏳")
                    markdown += f"## {emoji} {section.title}\n\n"
                    markdown += f"{section.content}\n\n---\n\n"

            return markdown.strip()

    def get_debug_view(self) -> str:
        """Vrátí kompletní interní stav s debug informacemi"""
        with self.lock:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            debug_info = f"""# 🔧 DEBUG VIEW - {self.metadata.project_name}

## 📊 Metadata
- **Project ID:** {self.metadata.project_id}
- **Session ID:** {self.metadata.session_id}
- **Created:** {self.metadata.created_at.strftime("%Y-%m-%d %H:%M:%S")}
- **Last Updated:** {current_time}
- **Current Phase:** {self.metadata.current_phase.value}
- **Total Versions:** {self.metadata.total_versions}

## 📋 Sections Detail

"""

            for section_id, section in self.sections.items():
                debug_info += f"""### {section.title} (`{section_id}`)
- **Owner:** {section.owner_agent}
- **Status:** {section.status.value}
- **Version:** {section.version}
- **Last Updated:** {section.last_updated.strftime("%Y-%m-%d %H:%M:%S")}
- **Dependencies:** {', '.join(section.dependencies) if section.dependencies else 'None'}

**Content:**
```
{section.content}
```

"""
                if section.reasoning:
                    debug_info += f"""**Agent Reasoning:**
```
{section.reasoning}
```

"""
                debug_info += "---\n\n"

            # Historie verzí
            if self.version_history:
                debug_info += "## 📚 Version History\n\n"
                for version in self.version_history[-5:]:  # Posledních 5 verzí
                    debug_info += f"- **{version.version_id}** ({version.agent_id}) - {version.description} - {version.timestamp.strftime('%H:%M:%S')}\n"

            return debug_info

    def set_phase(self, phase: ProjectPhase, agent_id: str = "system"):
        """Změní fázi projektu"""
        with self.lock:
            old_phase = self.metadata.current_phase
            self.metadata.current_phase = phase
            self.metadata.last_updated = datetime.now()

            logger.info(f"📈 Fáze projektu změněna: {old_phase.value} → {phase.value} (by {agent_id})")

    def rollback_to_version(self, version_id: str) -> bool:
        """Vrátí plátno k předchozí verzi"""
        with self.lock:
            try:
                version = next((v for v in self.version_history if v.version_id == version_id), None)
                if not version:
                    logger.error(f"❌ Verze {version_id} nenalezena")
                    return False

                # Vytvoření backup před rollback
                self._create_version_snapshot("system", f"Rollback to {version_id}")

                # Obnovení stavu
                self.sections = version.sections_snapshot.copy()
                self.metadata.last_updated = datetime.now()

                logger.info(f"🔄 Rollback k verzi {version_id} dokončen")
                return True

            except Exception as e:
                logger.error(f"❌ Chyba při rollback: {e}")
                return False

    def get_section_content(self, section_id: str) -> Optional[str]:
        """Vrátí obsah konkrétní sekce"""
        with self.lock:
            section = self.sections.get(section_id)
            return section.content if section else None

    def get_sections_by_agent(self, agent_id: str) -> Dict[str, CanvasSection]:
        """Vrátí všechny sekce vlastněné agentem"""
        with self.lock:
            return {sid: section for sid, section in self.sections.items()
                   if section.owner_agent == agent_id}

    def get_sections_by_status(self, status: SectionStatus) -> Dict[str, CanvasSection]:
        """Vrátí sekce podle stavu"""
        with self.lock:
            return {sid: section for sid, section in self.sections.items()
                   if section.status == status}

    def export_to_dict(self) -> Dict[str, Any]:
        """Export celého stavu do dictionary"""
        with self.lock:
            return {
                "metadata": asdict(self.metadata),
                "sections": {sid: asdict(section) for sid, section in self.sections.items()},
                "version_history": [asdict(v) for v in self.version_history]
            }

    def import_from_dict(self, data: Dict[str, Any]) -> bool:
        """Import stavu z dictionary"""
        try:
            with self.lock:
                # Import metadata
                self.metadata = CanvasMetadata(**data["metadata"])

                # Import sekcí
                self.sections = {}
                for sid, section_data in data["sections"].items():
                    self.sections[sid] = CanvasSection(**section_data)

                # Import historie (volitelné)
                if "version_history" in data:
                    self.version_history = [CanvasVersion(**v) for v in data["version_history"]]

                logger.info(f"✅ Canvas importován: {self.metadata.project_name}")
                return True

        except Exception as e:
            logger.error(f"❌ Chyba při importu canvas: {e}")
            return False


# ============================================================================
# FACTORY FUNKCE
# ============================================================================

def create_shared_canvas(project_name: str, session_id: str = None) -> SharedProjectCanvas:
    """Factory funkce pro vytvoření sdíleného plátna"""
    return SharedProjectCanvas(project_name=project_name, session_id=session_id)

def create_activity_tracker() -> AgentActivityTracker:
    """Factory funkce pro vytvoření activity trackeru"""
    return AgentActivityTracker()


# ============================================================================
# TESTOVACÍ FUNKCE
# ============================================================================

if __name__ == "__main__":
    # Test základní funkcionality
    print("🧪 Testování SharedProjectCanvas...")

    # Vytvoření plátna
    canvas = create_shared_canvas("Test Projekt", "test-session-123")
    tracker = create_activity_tracker()

    # Test aktualizace sekce
    tracker.log_activity("test_agent", "Aktualizuji přehled projektu", ActivityStatus.STARTED)

    success = canvas.update_by_agent(
        agent_id="test_agent",
        section="project_overview",
        content="Testovací projekt pro ověření funkcionality sdíleného plátna.",
        reasoning="Potřebujeme otestovat základní funkcionalita"
    )

    tracker.log_activity("test_agent", "Přehled projektu aktualizován", ActivityStatus.COMPLETED)

    print(f"✅ Aktualizace úspěšná: {success}")

    # Test client view
    print("\n📄 CLIENT VIEW:")
    print("=" * 60)
    print(canvas.get_client_view())

    # Test debug view
    print("\n🔧 DEBUG VIEW:")
    print("=" * 60)
    print(canvas.get_debug_view()[:500] + "...")

    print("\n🎉 Test dokončen!")
