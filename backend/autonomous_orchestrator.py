"""
Autonomní Orchestrator - Nová architektura s jedním super-agentem
<PERSON> fix<PERSON> a <PERSON>ech<PERSON>z<PERSON> na dynamic<PERSON>, cílově orientované p<PERSON>ánování.
"""

import logging
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime
import uuid

from openai import OpenAI
import os
# from tools.knowledge_base_tool import KnowledgeBaseTool
# from tools.best_practices_tool import BestPracticesTool

logger = logging.getLogger(__name__)

class AutonomousOrchestrator:
    """
    Autonomní orchestrator používají<PERSON>í jednoho super-agenta
    pro dynamické řešení komplexních požadavků.
    """
    
    def __init__(self):
        self.agents_config = self._load_agents_config()
        self.tasks_config = self._load_tasks_config()
        self.tools = self._initialize_tools()
        self.openai_client = self._initialize_openai()
        
    def _load_agents_config(self) -> Dict[str, Any]:
        """Načte konfiguraci autonomních agentů"""
        config_path = Path(__file__).parent / "agents" / "autonomous_agents.yaml"
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except Exception as e:
            logger.error(f"❌ Chyba při načítání konfigurace agentů: {e}")
            return {}
    
    def _load_tasks_config(self) -> Dict[str, Any]:
        """Načte konfiguraci úkolů"""
        config_path = Path(__file__).parent / "tasks" / "specialist_tasks.yaml"
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except Exception as e:
            logger.error(f"❌ Chyba při načítání konfigurace úkolů: {e}")
            return {}
    
    def _initialize_tools(self) -> Dict[str, Any]:
        """Inicializuje dostupné nástroje"""
        tools = {}

        # Pokus o načtení domain-aware best practices manageru
        try:
            from tools.best_practices_simple import DomainAwareBestPracticesSystem
            tools['best_practices_manager'] = DomainAwareBestPracticesSystem()
            logger.info("✅ Domain-Aware Best Practices manager inicializován")
        except Exception as e:
            logger.warning(f"⚠️ Domain-Aware Best Practices manager není dostupný: {e}")
            # Fallback na původní verzi
            try:
                from test_best_practices_tools import BestPracticesManager
                tools['best_practices_manager'] = BestPracticesManager()
                logger.info("✅ Fallback Best Practices manager inicializován")
            except Exception as e2:
                logger.warning(f"⚠️ Ani fallback Best Practices manager není dostupný: {e2}")

        return tools

    def _initialize_openai(self):
        """Inicializuje OpenAI klienta"""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            logger.warning("⚠️ OPENAI_API_KEY není nastavený, používám mock režim")
            return None

        return OpenAI(api_key=api_key)
    
    def _get_agent_prompt(self) -> str:
        """Získá prompt pro autonomního agenta"""
        agent_config = self.agents_config.get('autonomous_project_manager', {})

        if not agent_config:
            logger.error("❌ Konfigurace autonomního agenta nenalezena!")
            return "Jsi autonomní projektový manažer. Vyřeš uživatelův požadavek kompletně a profesionálně."

        return f"""
{agent_config.get('role', 'Autonomní Projektový Manažer')}

GOAL: {agent_config.get('goal', 'Vyřešit uživatelův požadavek kompletně')}

BACKSTORY: {agent_config.get('backstory', 'Jsi zkušený konzultant.')}

Dostupné nástroje:
- knowledge_base_search: Pro hledání relevantních informací
- best_practices_search: Pro osvědčené postupy

Tvým úkolem je vyřešit uživatelův požadavek autonomně, holisticky a profesionálně.
"""
    
    def _call_openai_agent(self, initial_request: str, **kwargs) -> str:
        """Volá OpenAI API s autonomním agentem"""
        if not self.openai_client:
            logger.warning("⚠️ OpenAI není dostupné, používám fallback")
            return self._create_fallback_response(initial_request)

        try:
            # Získání agent promptu
            agent_prompt = self._get_agent_prompt()

            # Příprava kontextu s nástroji
            tools_context = self._prepare_tools_context(initial_request)

            # Sestavení kompletního promptu
            full_prompt = f"""
{agent_prompt}

UŽIVATELŮV POŽADAVEK: {initial_request}

KONTEXT Z NÁSTROJŮ:
{tools_context}

INSTRUKCE:
1. Analyzuj požadavek holisticky
2. Vytvoř si mentální plán řešení
3. Použij dostupné informace z nástrojů
4. Dodej kompletní, profesionální výstup

FORMÁT ODPOVĚDI:
VŽDY odpověz POUZE validním JSON objektem s těmito klíči:
- "chat_response": Krátká, přirozená odpověď pro uživatele (max 200 slov)
- "canvas_content": Kompletní strukturovaný Markdown dokument s detailním návrhem (POVINNÉ!)
- "thinking": Tvůj myšlenkový proces

DŮLEŽITÉ PRAVIDLO: canvas_content NESMÍ být prázdný! Vždy vytvoř strukturovaný Markdown dokument.

PŘÍKLAD SPRÁVNÉHO FORMÁTU:
{{
  "chat_response": "Připravil jsem pro vás kompletní návrh ankety o spokojenosti zaměstnanců. Detaily najdete v projektovém plátně.",
  "canvas_content": "# 📋 Návrh Ankety o Spokojenosti Zaměstnanců\\n\\n## 🎯 Cíl\\nZjistit úroveň spokojenosti zaměstnanců...\\n\\n## 📊 Struktura\\n### 1. Úvod\\n### 2. Otázky\\n### 3. Demografie",
  "thinking": "Uživatel chce anketu o spokojenosti zaměstnanců. Vytvořím kompletní strukturovaný návrh."
}}

KRITICKÉ:
1. Odpověz POUZE JSON objektem, žádný další text!
2. canvas_content MUSÍ obsahovat strukturovaný Markdown dokument!
3. Nepoužívej prázdné hodnoty!
"""

            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": full_prompt},
                    {"role": "user", "content": initial_request}
                ],
                temperature=0.7,
                max_tokens=2000
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"❌ Chyba při volání OpenAI: {e}")
            return self._create_fallback_response(initial_request)
    
    def _prepare_tools_context(self, request: str) -> str:
        """Připraví kontext z dostupných nástrojů"""
        context_parts = [f"Požadavek: {request}"]

        # Domain-aware best practices kontext
        if 'best_practices_manager' in self.tools:
            try:
                bp_manager = self.tools['best_practices_manager']
                # Detekce domény pro best practices
                detected_domain = self._detect_domain(request)

                bp_results = bp_manager.find_relevant_best_practices(
                    current_situation_description=request,
                    match_count=2,
                    match_threshold=0.7,
                    domain=detected_domain
                )
                if bp_results and not bp_results.startswith("❌"):
                    context_parts.append(f"RELEVANTNÍ ZKUŠENOSTI:\n{bp_results}")
                else:
                    context_parts.append("ZKUŠENOSTI: Žádné relevantní zkušenosti z minulosti nenalezeny.")
            except Exception as e:
                logger.warning(f"⚠️ Chyba při získávání best practices: {e}")
                context_parts.append("ZKUŠENOSTI: Nedostupné")

        context_parts.append("KONTEXT: Autonomní AI agent s přístupem k obecným znalostem a systému učení ze zkušeností.")

        return "\n\n".join(context_parts)

    def _create_fallback_response(self, request: str) -> str:
        """Vytvoří fallback odpověď když AI není dostupné"""
        return f"""{{
    "chat_response": "Rozumím vašemu požadavku: '{request}'. Momentálně pracuji v omezeném režimu. Můžete mi prosím poskytnout více detailů, abych vám mohl lépe pomoct?",
    "canvas_content": "# 📋 Analýza Požadavku\\n\\n## 🎯 Požadavek\\n{request}\\n\\n## ⚠️ Status\\nSystém pracuje v omezeném režimu. Pro plnou funkcionalnost je potřeba konfigurace AI modelu.",
    "thinking": "Systém pracuje bez AI modelu, poskytuje základní odpověď."
}}"""

    def _create_fallback_structured_response(self, request: str, session_id: str, ai_response: str = "") -> Dict[str, Any]:
        """Vytvoří strukturovanou fallback odpověď když AI nevrátí správný formát"""

        # Pokud máme AI odpověď, pokusíme se z ní extrahovat užitečný obsah
        if ai_response:
            chat_response = f"Připravuji pro vás odpověď na váš požadavek. {ai_response[:300]}..."
            canvas_content = f"""# 📋 Zpracování Požadavku

## 🎯 Váš Požadavek
{request}

## 🤖 AI Odpověď
{ai_response}

## 📊 Status
Zpracováno autonomním AI agentem (fallback režim)

---
*Aktualizováno: {datetime.now().strftime('%Y-%m-%d %H:%M')}*"""
        else:
            chat_response = "Rozumím vašemu požadavku. Připravuji pro vás detailní analýzu a návrh řešení."
            canvas_content = f"""# 📋 Analýza Požadavku

## 🎯 Požadavek
{request}

## 🔄 Status
Zpracovávám váš požadavek...

---
*Aktualizováno: {datetime.now().strftime('%Y-%m-%d %H:%M')}*"""

        domain = self._detect_domain(request)

        return {
            "session_id": session_id,
            "chat_response": chat_response,
            "canvas_content": canvas_content,
            "domain": domain,
            "domain_name": self._get_domain_name(domain),
            "is_complete": False,
            "completion_percentage": 50,
            "status": "processing"
        }

    def process_request(self, initial_request: str, **kwargs) -> Dict[str, Any]:
        """
        Hlavní metoda pro zpracování požadavku pomocí autonomní architektury

        Args:
            initial_request: Původní požadavek uživatele
            **kwargs: Další parametry (domain, request_type, atd.)

        Returns:
            Dict s výsledky zpracování
        """
        try:
            logger.info(f"🚀 Spouštím autonomní zpracování: {initial_request[:100]}...")

            # Vytvoření session ID
            session_id = f"autonomous-{uuid.uuid4().hex[:8]}"

            # Volání autonomního AI agenta
            logger.info("🎯 Volám autonomní AI agent...")
            ai_response = self._call_openai_agent(initial_request, **kwargs)

            # Zpracování výsledku
            return self._process_ai_response(ai_response, session_id, initial_request)

        except Exception as e:
            logger.error(f"❌ Chyba v autonomním orchestrátoru: {e}")
            return self._create_error_response(str(e), initial_request)
    
    def _process_ai_response(self, ai_response: str, session_id: str, initial_request: str) -> Dict[str, Any]:
        """Zpracuje odpověď od AI agenta a vytvoří strukturovanou odpověď"""
        try:
            # Pokus o parsování JSON odpovědi
            import json

            # Vyčištění odpovědi - odstranění markdown bloků pokud existují
            cleaned_response = ai_response.strip()
            if cleaned_response.startswith("```json"):
                cleaned_response = cleaned_response[7:]
            if cleaned_response.endswith("```"):
                cleaned_response = cleaned_response[:-3]
            cleaned_response = cleaned_response.strip()

            try:
                parsed_response = json.loads(cleaned_response)
                chat_response = parsed_response.get("chat_response", "")
                canvas_content = parsed_response.get("canvas_content", "")
                thinking = parsed_response.get("thinking", "")

                logger.info(f"🧠 AI myšlení: {thinking[:100]}...")

                # Debug logging
                logger.info(f"📊 Parsovaná odpověď - Chat: {len(chat_response)} znaků, Canvas: {len(canvas_content)} znaků")

                # Validace že máme potřebný obsah
                if not chat_response.strip() or not canvas_content.strip():
                    logger.warning(f"⚠️ AI odpověď neobsahuje požadovaný obsah - Chat: '{chat_response[:50]}', Canvas: '{canvas_content[:50]}'")
                    return self._create_fallback_structured_response(initial_request, session_id)

            except json.JSONDecodeError as e:
                # Pokud není JSON, vytvoříme strukturovanou odpověď
                logger.warning(f"⚠️ AI odpověď není validní JSON: {e}")
                logger.info(f"📝 Surová AI odpověď: {ai_response[:200]}...")
                return self._create_fallback_structured_response(initial_request, session_id, ai_response)

            # Detekce domény z požadavku
            domain = self._detect_domain(initial_request)

            # Uložení úspěšné zkušenosti do best practices
            self._save_successful_experience(initial_request, chat_response, canvas_content, domain)

            return {
                "session_id": session_id,
                "chat_response": chat_response,
                "canvas_content": canvas_content,
                "domain": domain,
                "domain_name": self._get_domain_name(domain),
                "is_complete": True,  # Autonomní agent by měl dokončit úkol
                "completion_percentage": 100,
                "status": "completed"
            }

        except Exception as e:
            logger.error(f"❌ Chyba při zpracování AI odpovědi: {e}")
            return self._create_error_response(str(e), initial_request)

    def _create_canvas_from_text(self, text: str, request: str) -> str:
        """Vytvoří canvas obsah z textové odpovědi"""
        return f"""# 📋 Analýza Požadavku

## 🎯 Původní Požadavek
{request}

## 🤖 AI Odpověď
{text}

## 📊 Status
Zpracováno autonomním AI agentem

---
*Aktualizováno: {datetime.now().strftime('%Y-%m-%d %H:%M')}*"""

    def _save_successful_experience(self, request: str, chat_response: str, canvas_content: str, domain: str):
        """Uloží úspěšnou zkušenost do best practices"""
        if 'best_practices_manager' not in self.tools:
            return

        try:
            bp_manager = self.tools['best_practices_manager']

            # Vytvoření popisu kontextu
            context_description = f"Požadavek na {domain}: {request[:200]}..."

            # Vytvoření popisu strategie
            successful_strategy = f"Autonomní AI agent úspěšně vyřešil požadavek. Chat odpověď: {chat_response[:100]}... Canvas obsah byl strukturovaný a obsahoval {len(canvas_content)} znaků."

            # Uložení domain-aware zkušenosti
            result = bp_manager.save_best_practice(
                context_description=context_description,
                successful_strategy=successful_strategy,
                associated_agent_role="AutonomousProjectManager",
                feedback_notes=[f"Doména: {domain}", f"Úspěšně dokončeno", f"Session: {datetime.now().isoformat()}"],
                success_rating=0.9,
                domain=domain,
                context="general"
            )

            logger.info(f"💾 Best practice uložena: {result}")

        except Exception as e:
            logger.warning(f"⚠️ Nepodařilo se uložit best practice: {e}")
    
    def _extract_chat_summary(self, markdown_content: str) -> str:
        """Extrahuje shrnutí pro chat z markdown obsahu"""
        lines = markdown_content.split('\n')
        
        # Hledáme první odstavec nebo shrnutí
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#') and not line.startswith('*') and len(line) > 50:
                return f"Připravil jsem pro vás kompletní návrh. Detaily najdete v projektovém plátně vpravo. {line[:200]}..."
        
        return "Připravil jsem pro vás kompletní návrh. Detaily najdete v projektovém plátně vpravo."
    
    def _detect_domain(self, request: str) -> str:
        """Detekuje doménu z požadavku"""
        request_lower = request.lower()
        
        if any(word in request_lower for word in ["anketa", "anketu", "průzkum", "výzkum", "spokojenost"]):
            return "onboarding_pruzkumy"
        elif any(word in request_lower for word in ["prodej", "sales", "klient", "zákazník", "obchod"]):
            return "sales_qualification"
        elif any(word in request_lower for word in ["rozpočet", "participace", "občan", "komunita"]):
            return "participativni_rozpocet"
        elif any(word in request_lower for word in ["podpora", "support", "problém", "pomoc"]):
            return "customer_support"
        else:
            return "onboarding_pruzkumy"  # Default
    
    def _get_domain_name(self, domain: str) -> str:
        """Vrátí lidsky čitelný název domény"""
        domain_names = {
            "onboarding_pruzkumy": "Onboarding Průzkumů",
            "sales_qualification": "Sales Qualification", 
            "participativni_rozpocet": "Participativní Rozpočet",
            "customer_support": "Customer Support"
        }
        return domain_names.get(domain, "Univerzální")
    
    def _create_error_response(self, error_message: str, initial_request: str) -> Dict[str, Any]:
        """Vytvoří chybovou odpověď"""
        return {
            "session_id": f"error-{uuid.uuid4().hex[:8]}",
            "chat_response": f"Omlouvám se, došlo k chybě při zpracování vašeho požadavku: {error_message}",
            "canvas_content": f"# ❌ Chyba\n\nDošlo k chybě při zpracování požadavku: {initial_request}\n\n**Chyba:** {error_message}",
            "domain": "error",
            "domain_name": "Chyba",
            "is_complete": False,
            "completion_percentage": 0,
            "status": "error"
        }
