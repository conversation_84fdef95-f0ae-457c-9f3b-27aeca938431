# Definice Úkolů pro "Výzkumnou Jednotku Alfa"

task_analyze_and_strategize:
  description: >
    <PERSON><PERSON><PERSON> celou dosavadní konverzaci {chat_history} a poslední vstup od klienta {user_input}.
    Prove<PERSON> h<PERSON>kov<PERSON> anal<PERSON>zu, porovnej se stavem projektu, identifikuj další
    nezbytný krok a vygeneruj strategický JSON pro další agenty.
  expected_output: >
    Jeden validní JSON objekt obsahující pole `action`, `payload` (pokud je relevantní)
    a kompletní aktualizovaný `brief_state`.
  agent: research_strategist_v1
  async_execution: false

task_generate_client_response:
  description: >
    Vezmi strategický JSON výstup z předchozího úkolu a zformuluj
    přirozenou, empatickou a jasnou textovou odpověď pro klienta. Musí přesně
    reflektovat `action` a `payload` ze vstupu.

    DŮLEŽITÉ: Vrať pouze čistý text od<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> "Thought:" nebo "Action:" prefixy!
  expected_output: >
    Finální textová zpráva, která se zobrazí klientovi v chatu. Pouze čistý text.
  agent: communication_expert_v1
  context:
    - task_analyze_and_strategize # Tento úkol potřebuje výstup z předchozího
  async_execution: false

task_update_project_canvas:
  description: >
    Vezmi strategický JSON výstup z předchozího úkolu a konkrétně jeho část `brief_state`.
    Převeď ho na aktualizovaný, přehledný a profesionálně formátovaný Markdown dokument
    podle vzoru z tvého backstory. Použij vizuální indikátory (✅, ⏳, ➕) pro znázornění stavu.

    DŮLEŽITÉ: Vrať pouze čistý Markdown text, žádné "Thought:" nebo "Action:" prefixy!
  expected_output: >
    Čistý textový řetězec ve formátu Markdown, který reprezentuje aktuální stav projektu.
    Začíná "# 📋 Projektový Záměr:" a obsahuje všechny sekce podle vzoru.
  agent: data_architect_v1
  context:
    - task_analyze_and_strategize # Tento úkol potřebuje výstup z předchozího
  async_execution: false # Musí čekat na dokončení prvního úkolu

master_task_create_survey_proposal:
  description: >
    **Hlavní řídící úkol:** Orchestruj kompletní proces od přijetí počátečního požadavku
    až po dodání finálního návrhu průzkumu, včetně .lss souboru. Dynamicky spouštěj
    úkoly `task_analyze_and_strategize`, `task_generate_client_response` a `task_update_project_canvas`
    v opakujícím se cyklu, dokud není dosaženo stavu `COMPLETE`.
  expected_output: >
    Kompletní balíček pro klienta:
    1. Průvodní zpráva s popisem a doporučeními.
    2. Finální podoba "Projektového Plátna" v Markdown.
    3. Příloha se souborem `pruzkum_p21.lss` připraveným k importu.
  # Tento úkol není přiřazen agentovi, ale je spravován samotnou posádkou (crew).