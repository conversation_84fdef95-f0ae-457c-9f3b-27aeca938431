#!/usr/bin/env python3
"""
FastAPI server pro Matylda - Strategický AI Partner
Implementuje API endpointy pro komunikaci s frontendem
Používá nový ConversationalAgent systém místo CrewAI
"""

import os
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from dotenv import load_dotenv
from session_handler import MatyldaSession
from matylda_orchestrator import get_matylda_orchestrator
from alfa_orchestrator import AlfaOrchestrator

# Načtení konfigurace
load_dotenv()

# Logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Načtení konfigurací z environment variables
API_HOST = os.getenv("API_HOST", "0.0.0.0")
API_PORT = int(os.getenv("API_PORT", "8001"))
API_RELOAD = os.getenv("API_RELOAD", "True").lower() == "true"
API_TITLE = os.getenv("API_TITLE", "Matylda API")
API_DESCRIPTION = os.getenv("API_DESCRIPTION", "API pro strategického AI partnera Matylda")
API_VERSION = os.getenv("API_VERSION", "1.0.0")
MAX_ACTIVE_SESSIONS = int(os.getenv("MAX_ACTIVE_SESSIONS", "100"))

# Inicializace FastAPI
app = FastAPI(
    title=API_TITLE,
    description=API_DESCRIPTION,
    version=API_VERSION
)

# CORS middleware pro frontend komunikaci
# Dynamické generování povolených origins na základě konfigurace
FRONTEND_PORT = os.getenv("FRONTEND_PORT", "8080")
allowed_origins = [
    f"http://localhost:{FRONTEND_PORT}",
    f"http://127.0.0.1:{FRONTEND_PORT}",
    "http://localhost:8080",  # Fallback
    "http://localhost:8082",  # Fallback
    "http://localhost:8083",  # Fallback
    "http://127.0.0.1:8080",  # Fallback
    "http://127.0.0.1:8082",  # Fallback
    "http://127.0.0.1:8083",  # Fallback
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Úložiště aktivních session
active_sessions: Dict[str, MatyldaSession] = {}

# Pydantic modely pro API
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    request_type: Optional[str] = None  # Nové pole pro určení domény

class ChatResponse(BaseModel):
    session_id: str
    question: Optional[str] = None  # Zachováno pro kompatibilitu
    message: Optional[str] = None   # Zachováno pro kompatibilitu
    chat_response: Optional[str] = None  # Nové pole pro chat odpověď
    canvas_content: Optional[str] = None  # Nové pole pro canvas obsah
    final_analysis: Optional[Dict[str, Any]] = None
    status: str
    is_complete: bool
    completion_percentage: Optional[int] = None  # Procento dokončení
    domain: Optional[str] = None  # Nové pole pro doménu
    domain_name: Optional[str] = None  # Nové pole pro název domény

class StartConversationRequest(BaseModel):
    initial_request: str

class StartConversationResponse(BaseModel):
    session_id: str
    question: str
    status: str

class AnswerRequest(BaseModel):
    session_id: str
    answer: str

class AnswerResponse(BaseModel):
    session_id: str
    question: Optional[str] = None
    final_analysis: Optional[Dict[str, Any]] = None
    status: str
    is_complete: bool

class SessionStatusResponse(BaseModel):
    session_id: str
    exists: bool
    summary: Optional[Dict[str, Any]] = None

# API Endpointy

@app.get("/")
async def root():
    """Základní endpoint pro ověření funkčnosti API"""
    return {
        "message": "Matylda API je aktivní",
        "version": API_VERSION,
        "status": "running"
    }

@app.post("/chat/universal", response_model=ChatResponse)
async def universal_chat(request: ChatRequest):
    """
    Univerzální chat endpoint používající MatyldaOrchestrator.
    Automaticky určuje doménu a sestavuje příslušný tým agentů.
    """
    try:
        matylda_orch = get_matylda_orchestrator()

        if not request.session_id:
            # Nová konverzace
            logger.info(f"🆕 Nová univerzální konverzace: {request.message[:50]}...")

            result = matylda_orch.process_request(
                initial_request=request.message,
                request_type=request.request_type
            )

            # Vytvoření session pro pokračování konverzace
            session_id = result.get("session_id", f"session-{uuid.uuid4().hex[:8]}")
            if session_id not in active_sessions:
                # Vytvoření MatyldaSession pro pokračování
                matylda_session = MatyldaSession(request.message)
                active_sessions[session_id] = matylda_session
                logger.info(f"✅ Vytvořena session: {session_id}")

            return ChatResponse(
                session_id=session_id,
                question=result.get("chat_response"),  # Kompatibilita
                chat_response=result.get("chat_response"),
                canvas_content=result.get("canvas_content"),
                message=None,
                final_analysis=None,
                status="active",
                is_complete=result.get("is_complete", False),
                completion_percentage=result.get("completion_percentage", 0),
                domain=result.get("domain"),
                domain_name=result.get("domain_name")
            )
        else:
            # Pokračování konverzace - zatím fallback na původní systém
            logger.info(f"🔄 Pokračování univerzální konverzace {request.session_id}")

            if request.session_id in active_sessions:
                matylda_session = active_sessions[request.session_id]
                result = matylda_session.process_next_step(request.message)

                return ChatResponse(
                    session_id=request.session_id,
                    question=result.get("chat_response"),
                    chat_response=result.get("chat_response"),
                    canvas_content=result.get("canvas_content"),
                    message=None,
                    final_analysis=matylda_session.final_analysis if result.get("is_complete") else None,
                    status="completed" if result.get("is_complete") else "active",
                    is_complete=result.get("is_complete", False),
                    completion_percentage=result.get("completion_percentage", 0),
                    domain="onboarding_pruzkumy",  # Zatím hardcoded
                    domain_name="Onboarding Průzkumů"
                )
            else:
                raise HTTPException(status_code=404, detail="Session nenalezena")

    except Exception as e:
        logger.error(f"❌ Chyba v univerzálním chat endpointu: {e}")
        return ChatResponse(
            session_id=request.session_id or "error",
            chat_response=f"Omlouvám se, došlo k chybě: {str(e)}",
            canvas_content="# ❌ Chyba\n\nDošlo k chybě při zpracování.",
            status="error",
            is_complete=False,
            completion_percentage=0
        )

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """
    Hlavní chat endpoint - automaticky spravuje session
    Pokud session_id není poskytnut, vytvoří novou session
    Pokud je poskytnut, pokračuje ve stávající konverzaci
    """
    try:
        session_id = request.session_id

        # Pokud session_id není poskytnut, vytvoříme novou session
        if not session_id:
            # Kontrola limitu aktivních sessions
            if len(active_sessions) >= MAX_ACTIVE_SESSIONS:
                raise HTTPException(status_code=429, detail=f"Překročen limit aktivních sessions ({MAX_ACTIVE_SESSIONS})")

            # Vytvoření nové session
            session_id = str(uuid.uuid4())
            matylda_session = MatyldaSession(request.message)

            # Získání první otázky
            question = matylda_session.get_first_question()

            # Uložení session
            active_sessions[session_id] = matylda_session

            return ChatResponse(
                session_id=session_id,
                question=question,  # Kompatibilita
                chat_response=question,  # Nové pole
                canvas_content=matylda_session._get_fallback_canvas(),  # Canvas obsah
                message=None,
                final_analysis=None,
                status="active",
                is_complete=False,
                completion_percentage=0
            )

        # Pokračování ve stávající session
        else:
            # Ověření existence session
            if session_id not in active_sessions:
                raise HTTPException(status_code=404, detail="Session nenalezena")

            matylda_session = active_sessions[session_id]

            # Zpracování zprávy pomocí SpecialistOrchestrator
            result = matylda_session.process_next_step(request.message)

            # Kontrola, zda je konverzace dokončena
            if result.get("is_complete", False):
                final_analysis = matylda_session.get_final_analysis()
                return ChatResponse(
                    session_id=session_id,
                    question=None,
                    message=result["chat_response"],  # Kompatibilita
                    chat_response=result["chat_response"],
                    canvas_content=result["canvas_content"],
                    final_analysis=final_analysis,
                    status="completed",
                    is_complete=True,
                    completion_percentage=result.get("completion_percentage", 100)
                )
            else:
                # Pokračování v konverzaci
                return ChatResponse(
                    session_id=session_id,
                    question=result["chat_response"],  # Kompatibilita
                    message=None,
                    chat_response=result["chat_response"],
                    canvas_content=result["canvas_content"],
                    final_analysis=None,
                    status="active",
                    is_complete=False,
                    completion_percentage=result.get("completion_percentage", 0)
                )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při zpracování chat zprávy: {str(e)}")

@app.post("/conversation/start", response_model=StartConversationResponse)
async def start_conversation(request: StartConversationRequest):
    """
    Zahájení nové konverzace s Matylda systémem
    """
    try:
        # Kontrola limitu aktivních sessions
        if len(active_sessions) >= MAX_ACTIVE_SESSIONS:
            raise HTTPException(status_code=429, detail=f"Překročen limit aktivních sessions ({MAX_ACTIVE_SESSIONS})")

        # Vytvoření nové session
        session_id = str(uuid.uuid4())
        matylda_session = MatyldaSession(request.initial_request)

        # Získání první otázky
        question = matylda_session.get_first_question()

        # Uložení session
        active_sessions[session_id] = matylda_session
        
        return StartConversationResponse(
            session_id=session_id,
            question=question,
            status="active"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při zahájení konverzace: {str(e)}")

@app.post("/conversation/answer", response_model=AnswerResponse)
async def process_answer(request: AnswerRequest):
    """
    Zpracování odpovědi uživatele a získání další otázky nebo finálního výsledku
    """
    try:
        # Ověření existence session
        if request.session_id not in active_sessions:
            raise HTTPException(status_code=404, detail="Session nenalezena")
        
        matylda_session = active_sessions[request.session_id]

        # Zpracování odpovědi
        result = matylda_session.process_next_step(request.answer)

        # Kontrola, zda je konverzace dokončena
        if matylda_session.is_session_complete():
            final_analysis = matylda_session.get_final_analysis()
            return AnswerResponse(
                session_id=request.session_id,
                question=None,
                final_analysis=final_analysis,
                status="completed",
                is_complete=True
            )
        else:
            # Pokračování v konverzaci
            return AnswerResponse(
                session_id=request.session_id,
                question=result,
                final_analysis=None,
                status="active",
                is_complete=False
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při zpracování odpovědi: {str(e)}")

@app.get("/conversation/{session_id}/status", response_model=SessionStatusResponse)
async def get_session_status(session_id: str):
    """
    Získání stavu konkrétní session
    """
    try:
        if session_id not in active_sessions:
            return SessionStatusResponse(
                session_id=session_id,
                exists=False,
                summary=None
            )
        
        matylda_session = active_sessions[session_id]
        summary = matylda_session.get_session_summary()
        
        return SessionStatusResponse(
            session_id=session_id,
            exists=True,
            summary=summary
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při získávání stavu session: {str(e)}")

@app.delete("/conversation/{session_id}")
async def delete_session(session_id: str):
    """
    Smazání session z paměti
    """
    try:
        if session_id in active_sessions:
            del active_sessions[session_id]
            return {"message": f"Session {session_id} byla smazána", "status": "deleted"}
        else:
            raise HTTPException(status_code=404, detail="Session nenalezena")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při mazání session: {str(e)}")

@app.get("/sessions")
async def list_active_sessions():
    """
    Seznam všech aktivních sessions
    """
    try:
        sessions_info = []
        for session_id, matylda_session in active_sessions.items():
            summary = matylda_session.get_session_summary()
            sessions_info.append({
                "session_id": session_id,
                "summary": summary
            })
        
        return {
            "active_sessions_count": len(active_sessions),
            "sessions": sessions_info
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při získávání seznamu sessions: {str(e)}")

@app.get("/domains")
async def get_domains():
    """Vrátí seznam dostupných domén"""
    try:
        matylda_orch = get_matylda_orchestrator()
        domains = matylda_orch.get_available_domains()
        return {"domains": domains}
    except Exception as e:
        logger.error(f"❌ Chyba při získávání domén: {e}")
        return {"error": str(e), "domains": []}

@app.get("/domains/{domain_id}")
async def get_domain_info(domain_id: str):
    """Vrátí informace o konkrétní doméně"""
    try:
        matylda_orch = get_matylda_orchestrator()
        domain_info = matylda_orch.get_domain_info(domain_id)
        if domain_info:
            return domain_info
        else:
            raise HTTPException(status_code=404, detail="Doména nenalezena")
    except Exception as e:
        logger.error(f"❌ Chyba při získávání informací o doméně {domain_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """
    Health check endpoint pro monitoring
    """
    try:
        matylda_orch = get_matylda_orchestrator()
        domains = matylda_orch.get_available_domains()
        active_domains = [d for d in domains if d["available"]]

        return {
            "status": "healthy",
            "active_sessions": len(active_sessions),
            "max_sessions": MAX_ACTIVE_SESSIONS,
            "api_version": API_VERSION,
            "total_domains": len(domains),
            "active_domains": len(active_domains),
            "domains": [{"id": d["id"], "name": d["name"], "available": d["available"]} for d in domains]
        }
    except Exception as e:
        logger.error(f"❌ Chyba v health check: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "api_version": API_VERSION
        }


# ============================================================================
# CANVAS API ENDPOINTS
# ============================================================================

# Globální instance alfa orchestrátoru
alfa_orchestrator = AlfaOrchestrator()

@app.get("/canvas/{session_id}")
async def get_canvas_views(session_id: str, debug: bool = False):
    """
    Vrátí canvas views pro session

    Args:
        session_id: ID session
        debug: Pokud True, vrátí debug view, jinak client view
    """
    try:
        views = alfa_orchestrator.get_canvas_views(session_id)

        if not views or "error" in views:
            raise HTTPException(status_code=404, detail="Canvas pro session nenalezen")

        if debug:
            return {
                "session_id": session_id,
                "debug_view": views.get("debug_view", ""),
                "current_activities": views.get("current_activities", []),
                "activity_history": views.get("activity_history", []),
                "metadata": views.get("metadata", {})
            }
        else:
            return {
                "session_id": session_id,
                "client_view": views.get("client_view", ""),
                "metadata": views.get("metadata", {})
            }

    except Exception as e:
        logger.error(f"❌ Chyba při získávání canvas views: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/canvas/{session_id}/activities")
async def get_agent_activities(session_id: str):
    """
    Vrátí aktuální aktivity agentů pro session
    """
    try:
        activities = alfa_orchestrator.get_agent_activities(session_id)
        return {
            "session_id": session_id,
            "activities": activities,
            "count": len(activities)
        }

    except Exception as e:
        logger.error(f"❌ Chyba při získávání agent activities: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/canvas/{session_id}/debug")
async def get_debug_canvas(session_id: str):
    """
    Vrátí debug pohled na canvas
    """
    try:
        debug_view = alfa_orchestrator.get_debug_canvas(session_id)
        return {
            "session_id": session_id,
            "debug_view": debug_view
        }

    except Exception as e:
        logger.error(f"❌ Chyba při získávání debug canvas: {e}")
        raise HTTPException(status_code=500, detail=str(e))

class CanvasToggleRequest(BaseModel):
    """Request model pro toggle debug módu"""
    debug_mode: bool

@app.post("/canvas/{session_id}/toggle")
async def toggle_canvas_mode(session_id: str, request: CanvasToggleRequest):
    """
    Toggle mezi debug a production módem pro canvas
    """
    try:
        views = alfa_orchestrator.get_canvas_views(session_id)

        if not views or "error" in views:
            raise HTTPException(status_code=404, detail="Canvas pro session nenalezen")

        if request.debug_mode:
            return {
                "session_id": session_id,
                "mode": "debug",
                "debug_view": views.get("debug_view", ""),
                "current_activities": views.get("current_activities", []),
                "activity_history": views.get("activity_history", []),
                "metadata": views.get("metadata", {})
            }
        else:
            return {
                "session_id": session_id,
                "mode": "production",
                "client_view": views.get("client_view", ""),
                "metadata": views.get("metadata", {})
            }

    except Exception as e:
        logger.error(f"❌ Chyba při toggle canvas módu: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Spuštění serveru
if __name__ == "__main__":
    import uvicorn

    print("🚀 Spouštím Matylda API server...")
    print(f"📖 Dokumentace dostupná na: http://localhost:{API_PORT}/docs")
    print(f"🔍 Health check: http://localhost:{API_PORT}/health")
    print(f"⚙️  Konfigurace: Host={API_HOST}, Port={API_PORT}, Reload={API_RELOAD}")
    print(f"📊 Limity: Max sessions={MAX_ACTIVE_SESSIONS}")

    uvicorn.run(
        "api_server:app",
        host=API_HOST,
        port=API_PORT,
        reload=API_RELOAD
    )
