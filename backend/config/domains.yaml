# Konfigurace domén pro univerzální Matylda platformu
# Každ<PERSON> doména definuje svou architekturu, agenty a znalostní báze

domains:
  # <PERSON><PERSON><PERSON> pro onboarding průzkumů - nová autonomní implementace
  onboarding_pruzkumy:
    name: "Onboarding Průzku<PERSON>ů"
    description: "Specializace na přípravu a plánování výzkumných projektů"
    orchestrator_type: "AutonomousOrchestrator"
    
    # Konfigurace agentů a úkolů
    agents_config: "specialist_agents.yaml"
    tasks_config: "specialist_tasks.yaml"
    crews_config: "specialist_crew.yaml"
    
    # Znalostní báze specifické pro tuto doménu
    knowledge_bases:
      - id: "metodologie_pruzkumy"
        table_name: "kb_onboarding_pruzkumy"
        query_function: "match_onboarding_pruzkumy"
        description: "Metodologie a best practices pro průzkumy"
      - id: "statisticke_metody"
        table_name: "kb_statistika"
        query_function: "match_statistika"
        description: "Statistické metody a analýzy"
    
    # Best practices kontext
    best_practices_context: "onboarding_pruzkumy"
    
    # Výchozí brief struktura
    brief_structure:
      - projekt_cil
      - klicove_rozhodnuti
      - cilova_skupina
      - pozadovane_analyzy
      - casovy_ramec
      - rozpocet
    
    # API endpoints
    api_prefix: "/onboarding"
    
    # Aktivní/neaktivní
    enabled: true

  # Doména pro sales qualification - autonomní implementace
  sales_qualification:
    name: "Sales Qualification"
    description: "Kvalifikace obchodních příležitostí a lead scoring"
    orchestrator_type: "AutonomousOrchestrator"
    
    agents_config: "sales_agents.yaml"
    tasks_config: "sales_tasks.yaml"
    crews_config: "sales_crew.yaml"
    
    knowledge_bases:
      - id: "sales_materials"
        table_name: "kb_sales_materials"
        query_function: "match_sales_materials"
        description: "Prodejní materiály a strategie"
      - id: "competitor_analysis"
        table_name: "kb_competitors"
        query_function: "match_competitors"
        description: "Analýza konkurence"
    
    best_practices_context: "sales_qualification"
    
    brief_structure:
      - company_profile
      - business_needs
      - decision_makers
      - budget_range
      - timeline
      - competition
    
    api_prefix: "/sales"
    enabled: false  # Zatím neimplementováno

  # Doména pro participativní rozpočet - budoucí implementace
  participativni_rozpocet:
    name: "Participativní Rozpočet"
    description: "Facilitace participativního rozpočtování a občanské participace"
    orchestrator_type: "ParticipationOrchestrator"
    
    agents_config: "participation_agents.yaml"
    tasks_config: "participation_tasks.yaml"
    crews_config: "participation_crew.yaml"
    
    knowledge_bases:
      - id: "participace_metodiky"
        table_name: "kb_participace"
        query_function: "match_participace"
        description: "Metodiky participativních procesů"
      - id: "rozpocet_pravidla"
        table_name: "kb_rozpocet"
        query_function: "match_rozpocet"
        description: "Pravidla a postupy rozpočtování"
    
    best_practices_context: "participativni_rozpocet"
    
    brief_structure:
      - participace_cil
      - cilova_komunita
      - rozpocet_objem
      - kategorie_projektu
      - hodnotici_kriteria
      - harmonogram
    
    api_prefix: "/participace"
    enabled: false  # Budoucí implementace

  # Doména pro customer support - budoucí implementace
  customer_support:
    name: "Customer Support"
    description: "Inteligentní zákaznická podpora a řešení problémů"
    orchestrator_type: "SupportOrchestrator"
    
    agents_config: "support_agents.yaml"
    tasks_config: "support_tasks.yaml"
    crews_config: "support_crew.yaml"
    
    knowledge_bases:
      - id: "knowledge_base"
        table_name: "kb_support"
        query_function: "match_support"
        description: "Znalostní báze pro podporu"
      - id: "troubleshooting"
        table_name: "kb_troubleshooting"
        query_function: "match_troubleshooting"
        description: "Postupy řešení problémů"
    
    best_practices_context: "customer_support"
    
    brief_structure:
      - problem_description
      - customer_profile
      - urgency_level
      - previous_interactions
      - resolution_goal
    
    api_prefix: "/support"
    enabled: false  # Budoucí implementace

# Globální konfigurace
global_config:
  # Výchozí doména pokud není specifikována
  default_domain: "onboarding_pruzkumy"
  
  # Fallback orchestrátor
  fallback_orchestrator: "SpecialistOrchestrator"
  
  # Společné znalostní báze (dostupné všem doménám)
  shared_knowledge_bases:
    - id: "general_best_practices"
      table_name: "kb_general"
      query_function: "match_general"
      description: "Obecné best practices"
  
  # Společné nástroje
  shared_tools:
    - "knowledge_base_search"
    - "best_practices_search"
    - "save_best_practice"
  
  # API konfigurace
  api_config:
    base_url: "/api/v1"
    timeout: 30
    max_retries: 3
  
  # Logging
  logging:
    level: "INFO"
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Mapování request typů na domény
request_type_mapping:
  "průzkum": "onboarding_pruzkumy"
  "survey": "onboarding_pruzkumy"
  "research": "onboarding_pruzkumy"
  "spokojenost": "onboarding_pruzkumy"
  
  "sales": "sales_qualification"
  "lead": "sales_qualification"
  "qualification": "sales_qualification"
  "obchod": "sales_qualification"
  
  "participace": "participativni_rozpocet"
  "rozpočet": "participativni_rozpocet"
  "občané": "participativni_rozpocet"
  
  "support": "customer_support"
  "podpora": "customer_support"
  "problém": "customer_support"
  "help": "customer_support"

# Metadata
metadata:
  version: "2.0"
  created: "2025-07-10"
  description: "Konfigurace domén pro univerzální Matylda platformu"
  author: "Augment Agent"
