#!/usr/bin/env python3
"""
Alfa Team Orchestrator pro Matylda - Specializovaný orchestrátor pro Výzkumnou Jednotku Alfa
Používá research_crew_alfa z alfa_tým.yaml s detailním logováním
"""

import os
import json
import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process
from pydantic import BaseModel, Field
from config_loader import MatyldaConfigLoader
from multi_domain_rag import get_multi_domain_rag
from best_practices_system import get_best_practices_system

# Načtení konfigurace
load_dotenv()

# Logging s detailním výstupem
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BriefState(BaseModel):
    """Stav briefu pro alfa tým"""
    project_goal: str = ""
    target_audience: str = ""
    survey_topics: List[str] = []
    demographics: List[str] = []
    localization: str = ""
    timeline: str = ""
    technical_output: str = ""
    completion_percentage: int = 0

class OrchestrationResult(BaseModel):
    """Výsledek orchestrace alfa týmu"""
    chat_response: str
    canvas_content: str
    is_complete: bool
    completion_percentage: int
    session_id: str
    brief_state: BriefState

class AlfaOrchestrator:
    """
    Specializovaný orchestrátor pro Výzkumnou Jednotku Alfa
    Používá research_crew_alfa z alfa_tým.yaml s plným logováním
    """
    
    def __init__(self):
        self.config_loader = MatyldaConfigLoader()
        self.rag_system = get_multi_domain_rag()
        self.best_practices = get_best_practices_system()
        
        # Načtení konfigurace alfa týmu
        self._load_alfa_team_config()
        
        logger.info("🔬 Alfa Orchestrator inicializován")
    
    def _load_alfa_team_config(self):
        """Načte konfiguraci alfa týmu"""
        try:
            # Načtení agentů
            self.agents_config = self.config_loader.load_agents_config("alfa_agents.yaml")
            logger.info(f"✅ Načteno {len(self.agents_config)} agentů pro alfa tým")
            
            # Načtení úkolů
            self.tasks_config = self.config_loader.load_tasks_config("alfa_tasks.yaml")
            logger.info(f"✅ Načteno {len(self.tasks_config)} úkolů pro alfa tým")
            
            # Načtení crew konfigurace
            self.crews_config = self.config_loader.load_crews_config("alfa_tým.yaml")
            logger.info(f"✅ Načteno {len(self.crews_config)} crew konfigurací")
            
        except Exception as e:
            logger.error(f"❌ Chyba při načítání konfigurace alfa týmu: {e}")
            raise
    
    def process_message(self, user_message: str, chat_history: List[Dict[str, str]], 
                       brief_state: BriefState, session_id: str) -> OrchestrationResult:
        """
        Zpracuje zprávu pomocí alfa týmu s detailním logováním
        
        Args:
            user_message: Zpráva od uživatele
            chat_history: Historie konverzace
            brief_state: Aktuální stav briefu
            session_id: ID session
            
        Returns:
            Výsledek orchestrace s odpovědí a canvas obsahem
        """
        try:
            logger.info(f"🔬 === ALFA TÝM ORCHESTRACE ===")
            logger.info(f"📝 Zpráva: {user_message}")
            logger.info(f"🆔 Session: {session_id}")
            logger.info(f"📊 Brief state: {brief_state.model_dump()}")
            
            # Vytvoření alfa crew
            crew = self._create_alfa_crew(user_message, chat_history, brief_state)
            
            if not crew:
                logger.error("❌ Nepodařilo se vytvořit alfa crew")
                return self._create_error_response(session_id, brief_state)
            
            logger.info(f"✅ Alfa crew vytvořena s {len(crew.agents)} agenty a {len(crew.tasks)} úkoly")
            
            # Logování agentů
            for i, agent in enumerate(crew.agents, 1):
                logger.info(f"🤖 Agent {i}: {agent.role}")
                logger.info(f"   🎯 Cíl: {agent.goal}")
                logger.info(f"   🔧 Nástroje: {len(agent.tools)}")
            
            # Spuštění crew s detailním logováním
            logger.info("🎬 === SPOUŠTÍM ALFA CREW ===")
            
            # KRITICKÉ: verbose=True pro detailní logování
            crew.verbose = True
            
            result = crew.kickoff(inputs={
                'user_input': user_message,
                'chat_history': self._format_chat_history(chat_history, user_message),
                'brief_state': brief_state.model_dump_json(indent=2)
            })
            
            logger.info("🎯 === VÝSLEDEK ALFA CREW ===")
            logger.info(f"📄 Raw result: {str(result)}")
            
            # Zpracování výsledku
            return self._process_crew_result(result, session_id, brief_state)
            
        except Exception as e:
            logger.error(f"❌ Chyba při orchestraci alfa týmu: {e}")
            import traceback
            logger.error(f"📍 Traceback: {traceback.format_exc()}")
            return self._create_error_response(session_id, brief_state)
    
    def _create_alfa_crew(self, user_message: str, chat_history: List[Dict[str, str]],
                         brief_state: BriefState) -> Optional[Crew]:
        """Vytvoří alfa crew z konfigurace"""
        try:
            # Nejdříve vytvoříme agenty z alfa_agents.yaml
            agents = {}
            for agent_id in ["research_strategist_v1", "communication_expert_v1", "data_architect_v1"]:
                agent = self.config_loader.create_agent(agent_id, config_file="alfa_agents.yaml")
                if agent:
                    agents[agent_id] = agent
                    logger.info(f"✅ Agent {agent_id} vytvořen")
                else:
                    logger.error(f"❌ Nepodařilo se vytvořit agenta {agent_id}")
                    return None

            # Pak vytvoříme úkoly z alfa_tasks.yaml
            tasks = []

            # První úkol - analýza a strategie
            task1 = self.config_loader.create_task(
                "task_analyze_and_strategize",
                agents,
                config_file="alfa_tasks.yaml",
                user_input=user_message,
                chat_history=self._format_chat_history(chat_history, user_message),
                brief_state=brief_state.model_dump_json(indent=2)
            )
            if not task1:
                logger.error("❌ Nepodařilo se vytvořit task_analyze_and_strategize")
                return None
            tasks.append(task1)
            logger.info("✅ Úkol task_analyze_and_strategize vytvořen")

            # Druhý úkol - komunikace (závisí na prvním)
            task2 = self.config_loader.create_task(
                "task_generate_client_response",
                agents,
                config_file="alfa_tasks.yaml",
                user_input=user_message,
                chat_history=self._format_chat_history(chat_history, user_message),
                brief_state=brief_state.model_dump_json(indent=2),
                strategist_output="{task_analyze_and_strategize.output}"  # Placeholder pro CrewAI
            )
            if not task2:
                logger.error("❌ Nepodařilo se vytvořit task_generate_client_response")
                return None
            tasks.append(task2)
            logger.info("✅ Úkol task_generate_client_response vytvořen")

            # Třetí úkol - canvas (závisí na prvním)
            task3 = self.config_loader.create_task(
                "task_update_project_canvas",
                agents,
                config_file="alfa_tasks.yaml",
                user_input=user_message,
                chat_history=self._format_chat_history(chat_history, user_message),
                brief_state=brief_state.model_dump_json(indent=2),
                strategist_output="{task_analyze_and_strategize.output}"  # Placeholder pro CrewAI
            )
            if not task3:
                logger.error("❌ Nepodařilo se vytvořit task_update_project_canvas")
                return None
            tasks.append(task3)
            logger.info("✅ Úkol task_update_project_canvas vytvořen")

            # Nakonec vytvoříme crew
            crew = Crew(
                agents=list(agents.values()),
                tasks=tasks,
                process=Process.sequential,
                verbose=True,  # KRITICKÉ: zapnout verbose pro logování
                memory=False
            )

            logger.info(f"✅ Alfa crew vytvořena s {len(agents)} agenty a {len(tasks)} úkoly")
            return crew

        except Exception as e:
            logger.error(f"❌ Chyba při vytváření alfa crew: {e}")
            import traceback
            logger.error(f"📍 Traceback: {traceback.format_exc()}")
            return None
    
    def _format_chat_history(self, chat_history: List[Dict[str, str]], current_message: str) -> str:
        """Formátuje historii chatu pro agenty"""
        if not chat_history:
            return f"Aktuální zpráva: {current_message}"
        
        formatted = []
        for entry in chat_history[-5:]:  # Posledních 5 zpráv
            role = "Uživatel" if entry.get("type") == "user" else "Matylda"
            formatted.append(f"{role}: {entry.get('content', '')}")
        
        formatted.append(f"Uživatel: {current_message}")
        return "\n".join(formatted)
    
    def _process_crew_result(self, result, session_id: str, brief_state: BriefState) -> OrchestrationResult:
        """Zpracuje výsledek od alfa crew"""
        try:
            result_str = str(result)
            logger.info(f"🔍 Zpracovávám výsledek: {result_str[:200]}...")
            
            # Pro nyní jednoduché zpracování - v budoucnu můžeme parsovat JSON
            return OrchestrationResult(
                chat_response=result_str,
                canvas_content=self._generate_canvas_content(brief_state),
                is_complete=False,  # Alfa tým pracuje iterativně
                completion_percentage=brief_state.completion_percentage + 20,
                session_id=session_id,
                brief_state=brief_state
            )
            
        except Exception as e:
            logger.error(f"❌ Chyba při zpracování výsledku: {e}")
            return self._create_error_response(session_id, brief_state)
    
    def _generate_canvas_content(self, brief_state: BriefState) -> str:
        """Generuje canvas obsah"""
        return f"""# 📋 Projektový Záměr: Výzkumná Jednotka Alfa

*Poslední aktualizace: {datetime.now().strftime('%Y-%m-%d %H:%M')}*

---

### 🎯 Cíl Projektu
{brief_state.project_goal or "⏳ Čeká na definici"}

### 👥 Cílová Skupina
{brief_state.target_audience or "⏳ Čeká na definici"}

### 🗺️ Klíčové Oblasti Průzkumu
{chr(10).join([f"✅ {topic}" for topic in brief_state.survey_topics]) if brief_state.survey_topics else "⏳ Čeká na definici"}

### 📊 Demografie
{chr(10).join([f"✅ {demo}" for demo in brief_state.demographics]) if brief_state.demographics else "⏳ Čeká na definici"}

### 🗓️ Časový Rámec
{brief_state.timeline or "⏳ Čeká na definici"}

### ⚙️ Technický Výstup
{brief_state.technical_output or "⏳ Čeká na definici"}

---

**Dokončeno: {brief_state.completion_percentage}%**
"""
    
    def _create_error_response(self, session_id: str, brief_state: BriefState) -> OrchestrationResult:
        """Vytvoří chybovou odpověď"""
        return OrchestrationResult(
            chat_response="Omlouvám se, došlo k chybě při zpracování vašeho požadavku alfa týmem.",
            canvas_content="# ❌ Chyba\n\nDošlo k chybě při zpracování požadavku.",
            is_complete=False,
            completion_percentage=brief_state.completion_percentage,
            session_id=session_id,
            brief_state=brief_state
        )

# Factory funkce pro vytvoření orchestrátoru
def create_alfa_orchestrator() -> AlfaOrchestrator:
    """Vytvoří instanci alfa orchestrátoru"""
    return AlfaOrchestrator()

if __name__ == "__main__":
    # Test alfa orchestrátoru
    print("🧪 Testování Alfa Orchestrátoru...")
    
    orchestrator = create_alfa_orchestrator()
    
    # Test zpráva
    test_message = "Chci udělat průzkum spokojenosti občanů v Praze 21"
    test_brief = BriefState()
    
    result = orchestrator.process_message(
        user_message=test_message,
        chat_history=[],
        brief_state=test_brief,
        session_id="test-session"
    )
    
    print(f"✅ Test dokončen")
    print(f"📝 Odpověď: {result.chat_response[:100]}...")
    print(f"📊 Canvas: {len(result.canvas_content)} znaků")
