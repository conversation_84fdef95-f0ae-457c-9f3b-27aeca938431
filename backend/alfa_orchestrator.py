#!/usr/bin/env python3
"""
Alfa Team Orchestrator pro Matylda - Specializovaný orchestrátor pro Výzkumnou Jednotku Alfa
Používá research_crew_alfa z alfa_tým.yaml s detailním logováním
"""

import os
import json
import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process
from pydantic import BaseModel, Field
from config_loader import MatyldaConfigLoader
from multi_domain_rag import get_multi_domain_rag
from best_practices_system import get_best_practices_system
from canvas_integration import (
    CanvasOrchestrator, CanvasMiddleware,
    ActivityType, ActivityStatus, ProjectPhase
)

# Načtení konfigurace
load_dotenv()

# Logging s detailním výstupem
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BriefState(BaseModel):
    """Stav briefu pro alfa tým"""
    project_goal: str = ""
    target_audience: str = ""
    survey_topics: List[str] = []
    demographics: List[str] = []
    localization: str = ""
    timeline: str = ""
    technical_output: str = ""
    completion_percentage: int = 0

class OrchestrationResult(BaseModel):
    """Výsledek orchestrace alfa týmu"""
    chat_response: str
    canvas_content: str
    is_complete: bool
    completion_percentage: int
    session_id: str
    brief_state: BriefState

class AlfaOrchestrator:
    """
    Specializovaný orchestrátor pro Výzkumnou Jednotku Alfa
    Používá research_crew_alfa z alfa_tým.yaml s plným logováním
    """
    
    def __init__(self):
        self.config_loader = MatyldaConfigLoader()
        self.rag_system = get_multi_domain_rag()
        self.best_practices = get_best_practices_system()

        # Canvas systém
        self.canvas_orchestrator = CanvasOrchestrator()
        self.canvas_middleware = CanvasMiddleware(self.canvas_orchestrator)

        # Načtení konfigurace alfa týmu
        self._load_alfa_team_config()

        logger.info("🔬 Alfa Orchestrator inicializován s Canvas systémem")

    def get_canvas_views(self, session_id: str) -> Dict[str, Any]:
        """Vrátí všechny pohledy na canvas pro session"""
        return self.canvas_orchestrator.get_canvas_views(session_id)

    def get_debug_canvas(self, session_id: str) -> str:
        """Vrátí debug pohled na canvas"""
        views = self.canvas_orchestrator.get_canvas_views(session_id)
        return views.get("debug_view", "Debug canvas nedostupný")

    def get_agent_activities(self, session_id: str) -> List[Dict[str, Any]]:
        """Vrátí aktuální aktivity agentů"""
        views = self.canvas_orchestrator.get_canvas_views(session_id)
        activities = views.get("current_activities", [])

        # Konverze na dictionary pro JSON serialization
        return [
            {
                "agent_id": activity.agent_id,
                "activity_type": activity.activity_type.value,
                "description": activity.description,
                "status": activity.status.value,
                "timestamp": activity.timestamp.isoformat(),
                "duration": activity.duration
            }
            for activity in activities
        ]
        
        logger.info("🔬 Alfa Orchestrator inicializován")
    
    def _load_alfa_team_config(self):
        """Načte konfiguraci alfa týmu"""
        try:
            # Načtení agentů
            self.agents_config = self.config_loader.load_agents_config("alfa_agents.yaml")
            logger.info(f"✅ Načteno {len(self.agents_config)} agentů pro alfa tým")
            
            # Načtení úkolů
            self.tasks_config = self.config_loader.load_tasks_config("alfa_tasks.yaml")
            logger.info(f"✅ Načteno {len(self.tasks_config)} úkolů pro alfa tým")
            
            # Načtení crew konfigurace
            self.crews_config = self.config_loader.load_crews_config("alfa_tým.yaml")
            logger.info(f"✅ Načteno {len(self.crews_config)} crew konfigurací")
            
        except Exception as e:
            logger.error(f"❌ Chyba při načítání konfigurace alfa týmu: {e}")
            raise
    
    def process_message(self, user_message: str, chat_history: List[Dict[str, str]], 
                       brief_state: BriefState, session_id: str) -> OrchestrationResult:
        """
        Zpracuje zprávu pomocí alfa týmu s detailním logováním
        
        Args:
            user_message: Zpráva od uživatele
            chat_history: Historie konverzace
            brief_state: Aktuální stav briefu
            session_id: ID session
            
        Returns:
            Výsledek orchestrace s odpovědí a canvas obsahem
        """
        try:
            logger.info(f"🔬 === ALFA TÝM ORCHESTRACE ===")
            logger.info(f"📝 Zpráva: {user_message}")
            logger.info(f"🆔 Session: {session_id}")
            logger.info(f"📊 Brief state: {brief_state.model_dump()}")
            
            # Vytvoření alfa crew
            crew = self._create_alfa_crew(user_message, chat_history, brief_state)
            
            if not crew:
                logger.error("❌ Nepodařilo se vytvořit alfa crew")
                return self._create_error_response(session_id, brief_state)
            
            logger.info(f"✅ Alfa crew vytvořena s {len(crew.agents)} agenty a {len(crew.tasks)} úkoly")
            
            # Logování agentů
            for i, agent in enumerate(crew.agents, 1):
                logger.info(f"🤖 Agent {i}: {agent.role}")
                logger.info(f"   🎯 Cíl: {agent.goal}")
                logger.info(f"   🔧 Nástroje: {len(agent.tools)}")
            
            # Canvas integrace
            project_name = f"Projekt {session_id[:8]}"
            canvas = self.canvas_orchestrator.get_or_create_canvas(session_id, project_name)
            activity_tracker = self.canvas_orchestrator.get_activity_tracker(session_id)

            # Integrace agentů s canvas
            self.canvas_orchestrator.integrate_agents_with_canvas(crew.agents, session_id)

            # Spuštění crew s detailním logováním a canvas
            logger.info("🎬 === SPOUŠTÍM ALFA CREW S CANVAS ===")

            # KRITICKÉ: verbose=True pro detailní logování
            crew.verbose = True

            # Log začátku
            activity_tracker.log_activity(
                agent="alfa_orchestrator",
                activity="Spouštím crew execution",
                status=ActivityStatus.STARTED,
                activity_type=ActivityType.THINKING
            )

            result = crew.kickoff(inputs={
                'user_input': user_message,
                'chat_history': self._format_chat_history(chat_history, user_message),
                'brief_state': brief_state.model_dump_json(indent=2),
                'current_timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'strategist_output': "{task_analyze_and_strategize.output}"  # Placeholder pro CrewAI
            })

            # Log dokončení
            activity_tracker.log_activity(
                agent="alfa_orchestrator",
                activity="Crew execution dokončen",
                status=ActivityStatus.COMPLETED,
                activity_type=ActivityType.THINKING
            )

            logger.info("🎯 === VÝSLEDEK ALFA CREW ===")
            logger.info(f"📄 Raw result: {str(result)}")

            # Zpracování výsledku s canvas daty
            return self._process_crew_result_with_canvas(result, session_id, brief_state)
            
        except Exception as e:
            logger.error(f"❌ Chyba při orchestraci alfa týmu: {e}")
            import traceback
            logger.error(f"📍 Traceback: {traceback.format_exc()}")
            return self._create_error_response(session_id, brief_state)
    
    def _create_alfa_crew(self, user_message: str, chat_history: List[Dict[str, str]],
                         brief_state: BriefState) -> Optional[Crew]:
        """Vytvoří alfa crew z konfigurace"""
        try:
            # Nejdříve vytvoříme agenty z alfa_agents.yaml
            agents = {}
            for agent_id in ["research_strategist_v1", "communication_expert_v1", "data_architect_v1"]:
                agent = self.config_loader.create_agent(agent_id, config_file="alfa_agents.yaml")
                if agent:
                    agents[agent_id] = agent
                    logger.info(f"✅ Agent {agent_id} vytvořen")
                else:
                    logger.error(f"❌ Nepodařilo se vytvořit agenta {agent_id}")
                    return None

            # Pak vytvoříme úkoly z alfa_tasks.yaml
            tasks = []

            # První úkol - analýza a strategie
            task1 = self.config_loader.create_task(
                "task_analyze_and_strategize",
                agents,
                config_file="alfa_tasks.yaml",
                user_input=user_message,
                chat_history=self._format_chat_history(chat_history, user_message),
                brief_state=brief_state.model_dump_json(indent=2)
            )
            if not task1:
                logger.error("❌ Nepodařilo se vytvořit task_analyze_and_strategize")
                return None
            tasks.append(task1)
            logger.info("✅ Úkol task_analyze_and_strategize vytvořen")

            # Druhý úkol - komunikace (závisí na prvním)
            task2 = self.config_loader.create_task(
                "task_generate_client_response",
                agents,
                config_file="alfa_tasks.yaml",
                user_input=user_message,
                chat_history=self._format_chat_history(chat_history, user_message),
                brief_state=brief_state.model_dump_json(indent=2),
                strategist_output="{task_analyze_and_strategize.output}"  # Placeholder pro CrewAI
            )
            if not task2:
                logger.error("❌ Nepodařilo se vytvořit task_generate_client_response")
                return None
            tasks.append(task2)
            logger.info("✅ Úkol task_generate_client_response vytvořen")

            # Třetí úkol - canvas (závisí na prvním)
            task3 = self.config_loader.create_task(
                "task_update_project_canvas",
                agents,
                config_file="alfa_tasks.yaml",
                user_input=user_message,
                chat_history=self._format_chat_history(chat_history, user_message),
                brief_state=brief_state.model_dump_json(indent=2),
                strategist_output="{task_analyze_and_strategize.output}"  # Placeholder pro CrewAI
            )
            if not task3:
                logger.error("❌ Nepodařilo se vytvořit task_update_project_canvas")
                return None
            tasks.append(task3)
            logger.info("✅ Úkol task_update_project_canvas vytvořen")

            # Nakonec vytvoříme crew
            crew = Crew(
                agents=list(agents.values()),
                tasks=tasks,
                process=Process.sequential,
                verbose=True,  # KRITICKÉ: zapnout verbose pro logování
                memory=False
            )

            logger.info(f"✅ Alfa crew vytvořena s {len(agents)} agenty a {len(tasks)} úkoly")
            return crew

        except Exception as e:
            logger.error(f"❌ Chyba při vytváření alfa crew: {e}")
            import traceback
            logger.error(f"📍 Traceback: {traceback.format_exc()}")
            return None
    
    def _format_chat_history(self, chat_history: List[Dict[str, str]], current_message: str) -> str:
        """Formátuje historii chatu pro agenty"""
        if not chat_history:
            return f"Aktuální zpráva: {current_message}"
        
        formatted = []
        for entry in chat_history[-5:]:  # Posledních 5 zpráv
            role = "Uživatel" if entry.get("type") == "user" else "Matylda"
            formatted.append(f"{role}: {entry.get('content', '')}")
        
        formatted.append(f"Uživatel: {current_message}")
        return "\n".join(formatted)
    
    def _process_crew_result_with_canvas(self, result, session_id: str, brief_state: BriefState) -> OrchestrationResult:
        """Zpracuje výsledek od alfa crew s canvas daty"""
        try:
            result_str = str(result)
            logger.info(f"🔍 Zpracovávám výsledek s canvas: {result_str[:200]}...")

            # Získání canvas views
            canvas_views = self.canvas_orchestrator.get_canvas_views(session_id)

            return OrchestrationResult(
                chat_response=result_str,
                canvas_content=canvas_views.get("client_view", "Canvas nedostupný"),
                is_complete=False,  # Alfa tým pracuje iterativně
                completion_percentage=brief_state.completion_percentage + 20,
                session_id=session_id,
                brief_state=brief_state
            )

        except Exception as e:
            logger.error(f"❌ Chyba při zpracování výsledku s canvas: {e}")
            return self._create_error_response(session_id, brief_state)

    def _process_crew_result(self, result, session_id: str, brief_state: BriefState) -> OrchestrationResult:
        """Zpracuje výsledek od alfa crew (fallback bez canvas)"""
        try:
            result_str = str(result)
            logger.info(f"🔍 Zpracovávám výsledek: {result_str[:200]}...")

            # Pro nyní jednoduché zpracování - v budoucnu můžeme parsovat JSON
            return OrchestrationResult(
                chat_response=result_str,
                canvas_content=self._generate_canvas_content(brief_state),
                is_complete=False,  # Alfa tým pracuje iterativně
                completion_percentage=brief_state.completion_percentage + 20,
                session_id=session_id,
                brief_state=brief_state
            )

        except Exception as e:
            logger.error(f"❌ Chyba při zpracování výsledku: {e}")
            return self._create_error_response(session_id, brief_state)
    
    def _generate_canvas_content(self, brief_state: BriefState) -> str:
        """Generuje canvas obsah"""
        return f"""# 📋 Projektový Záměr: Výzkumná Jednotka Alfa

*Poslední aktualizace: {datetime.now().strftime('%Y-%m-%d %H:%M')}*

---

### 🎯 Cíl Projektu
{brief_state.project_goal or "⏳ Čeká na definici"}

### 👥 Cílová Skupina
{brief_state.target_audience or "⏳ Čeká na definici"}

### 🗺️ Klíčové Oblasti Průzkumu
{chr(10).join([f"✅ {topic}" for topic in brief_state.survey_topics]) if brief_state.survey_topics else "⏳ Čeká na definici"}

### 📊 Demografie
{chr(10).join([f"✅ {demo}" for demo in brief_state.demographics]) if brief_state.demographics else "⏳ Čeká na definici"}

### 🗓️ Časový Rámec
{brief_state.timeline or "⏳ Čeká na definici"}

### ⚙️ Technický Výstup
{brief_state.technical_output or "⏳ Čeká na definici"}

---

**Dokončeno: {brief_state.completion_percentage}%**
"""
    
    def _create_error_response(self, session_id: str, brief_state: BriefState) -> OrchestrationResult:
        """Vytvoří chybovou odpověď"""
        return OrchestrationResult(
            chat_response="Omlouvám se, došlo k chybě při zpracování vašeho požadavku alfa týmem.",
            canvas_content="# ❌ Chyba\n\nDošlo k chybě při zpracování požadavku.",
            is_complete=False,
            completion_percentage=brief_state.completion_percentage,
            session_id=session_id,
            brief_state=brief_state
        )

# Factory funkce pro vytvoření orchestrátoru
def create_alfa_orchestrator() -> AlfaOrchestrator:
    """Vytvoří instanci alfa orchestrátoru"""
    return AlfaOrchestrator()

if __name__ == "__main__":
    # Test alfa orchestrátoru
    print("🧪 Testování Alfa Orchestrátoru...")
    
    orchestrator = create_alfa_orchestrator()
    
    # Test zpráva
    test_message = "Chci udělat průzkum spokojenosti občanů v Praze 21"
    test_brief = BriefState()
    
    result = orchestrator.process_message(
        user_message=test_message,
        chat_history=[],
        brief_state=test_brief,
        session_id="test-session"
    )
    
    print(f"✅ Test dokončen")

    # Test canvas views
    canvas_views = orchestrator.get_canvas_views("test-session")
    print(f"📊 Canvas views: {list(canvas_views.keys())}")

    # Test debug canvas
    debug_canvas = orchestrator.get_debug_canvas("test-session")
    print(f"🔧 Debug canvas length: {len(debug_canvas)}")

    # Test agent activities
    activities = orchestrator.get_agent_activities("test-session")
    print(f"🤖 Agent activities: {len(activities)}")

    print(f"📝 Odpověď: {result.chat_response[:100]}...")
    print(f"📊 Canvas: {len(result.canvas_content)} znaků")
