/**
 * Matylda Frontend Chat Logic
 * Implementuje komunikaci s backend API a řízení chat rozhraní
 */

class MatyldaChat {
    constructor() {
        this.sessionId = null;
        this.isLoading = false;
        this.apiBaseUrl = this.getApiBaseUrl();
        
        // DOM elementy
        this.chatMessages = document.getElementById('chatMessages');
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.charCounter = document.getElementById('charCounter');
        this.sessionInfo = document.getElementById('sessionInfo');
        this.statusIndicator = document.getElementById('statusIndicator');
        this.loadingOverlay = document.getElementById('loadingOverlay');

        // Nové elementy pro v2.0
        this.domainSelect = document.getElementById('domainSelect');
        this.domainBadge = document.getElementById('domainBadge');
        this.progressBadge = document.getElementById('progressBadge');
        this.canvasContent = document.getElementById('canvasContent');
        
        this.initializeEventListeners();
        this.updateUI();
    }

    /**
     * Získání base URL pro API na základě konfigurace
     */
    getApiBaseUrl() {
        // Použij globální konfiguraci pokud je dostupná
        if (window.frontendConfig) {
            return window.frontendConfig.getApiBaseUrl();
        }

        // Fallback pro případ, že konfigurace není dostupná
        const hostname = window.location.hostname;
        const port = hostname === 'localhost' ? '8001' : '8001';
        const protocol = window.location.protocol === 'https:' ? 'https:' : 'http:';
        return `${protocol}//${hostname}:${port}`;
    }

    /**
     * Inicializace event listenerů
     */
    initializeEventListeners() {
        // Odeslání zprávy
        this.sendButton.addEventListener('click', () => this.sendMessage());
        
        // Enter pro nový řádek, Ctrl+Enter pro odeslání (Shift+Enter pro nový řádek)
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                if (e.ctrlKey || e.metaKey) {
                    // Ctrl+Enter nebo Cmd+Enter = odeslat
                    e.preventDefault();
                    this.sendMessage();
                } else if (!e.shiftKey) {
                    // Pouhý Enter = nový řádek (výchozí chování)
                    // Necháme prohlížeč zpracovat normálně
                }
            }
        });
        
        // Počítadlo znaků a UI update
        this.messageInput.addEventListener('input', () => {
            this.updateCharCounter();
            this.updateUI(); // Přidáno pro enable/disable tlačítka
        });

        // Auto-resize textarea
        this.messageInput.addEventListener('input', () => {
            this.autoResizeTextarea();
        });

        // Domain selector
        this.domainSelect.addEventListener('change', () => this.updateDomainBadge());

        // Resizer functionality
        this.initResizer();
    }

    /**
     * Aktualizace počítadla znaků
     */
    updateCharCounter() {
        const length = this.messageInput.value.length;
        this.charCounter.textContent = `${length}/2000`;
        
        if (length > 1800) {
            this.charCounter.style.color = 'var(--error-color)';
        } else if (length > 1500) {
            this.charCounter.style.color = 'var(--warning-color)';
        } else {
            this.charCounter.style.color = 'var(--text-muted)';
        }
    }

    /**
     * Auto-resize textarea
     */
    autoResizeTextarea() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 200) + 'px';
    }

    /**
     * Aktualizace domain badge
     */
    updateDomainBadge() {
        const selectedDomain = this.domainSelect.value;
        if (selectedDomain) {
            const domainNames = {
                'onboarding_pruzkumy': 'Onboarding Průzkumů',
                'research_public_opinion_municipal': 'Výzkumná Jednotka Alfa',
                'sales_qualification': 'Sales Qualification',
                'participativni_rozpocet': 'Participativní Rozpočet',
                'customer_support': 'Customer Support'
            };
            this.domainBadge.textContent = domainNames[selectedDomain] || selectedDomain;

            // Načtení help obsahu pro vybranou doménu
            this.loadDomainHelp(selectedDomain);
        } else {
            this.domainBadge.textContent = 'Auto-detekce';

            // Načtení obecného help obsahu
            this.loadDomainHelp(null);
        }
    }

    /**
     * Aktualizace canvas obsahu
     */
    updateCanvas(canvasContent, completionPercentage = 0) {
        console.warn('⚠️ updateCanvas v chat.js je deprecated - používá se Canvas Manager');

        // Pouze aktualizace progress badge (canvas obsah spravuje Canvas Manager)
        this.progressBadge.textContent = `${completionPercentage}%`;
    }

    /**
     * Jednoduchý Markdown to HTML converter
     */
    markdownToHtml(markdown) {
        return markdown
            .replace(/^### (.*$)/gim, '<h3>$1</h3>')
            .replace(/^## (.*$)/gim, '<h2>$1</h2>')
            .replace(/^# (.*$)/gim, '<h1>$1</h1>')
            .replace(/^\* (.*$)/gim, '<li>$1</li>')
            .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
            .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
            .replace(/\*(.*)\*/gim, '<em>$1</em>')
            .replace(/^---$/gim, '<hr>')
            .replace(/\n/gim, '<br>');
    }

    /**
     * Aktualizace UI stavů
     */
    updateUI() {
        const hasText = this.messageInput.value.trim().length > 0;
        this.sendButton.disabled = this.isLoading || !hasText;

        // Aktualizace session info
        if (this.sessionId) {
            this.sessionInfo.textContent = `Session: ${this.sessionId.substring(0, 8)}...`;
        } else {
            this.sessionInfo.textContent = 'Nová konverzace';
        }

        // Aktualizace status indikátoru (pokud existuje)
        if (this.statusIndicator) {
            const statusDot = this.statusIndicator.querySelector('.status-dot');
            const statusText = this.statusIndicator.querySelector('.status-text');

            if (statusDot && statusText) {
                if (this.isLoading) {
                    statusDot.className = 'status-dot loading';
                    statusText.textContent = 'Zpracovávám...';
                } else {
                    statusDot.className = 'status-dot';
                    statusText.textContent = 'Připraveno';
                }
            }
        }
    }

    /**
     * Zobrazení loading stavu - inline v chatu
     */
    showLoading() {
        this.isLoading = true;

        // Přidání thinking zprávy do chatu místo popup
        this.addThinkingMessage();

        this.updateUI();
    }

    /**
     * Skrytí loading stavu - odstranění thinking zprávy
     */
    hideLoading() {
        this.isLoading = false;

        // Odstranění thinking zprávy z chatu
        this.removeThinkingMessage();

        this.updateUI();
    }

    /**
     * Přidání thinking zprávy do chatu
     */
    addThinkingMessage() {
        const thinkingElement = document.createElement('div');
        thinkingElement.className = 'message assistant thinking-message';
        thinkingElement.id = 'thinking-message';

        thinkingElement.innerHTML = `
            <div class="message-content">
                <div class="thinking-indicator">
                    <div class="thinking-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <span class="thinking-text">🤖 Matylda přemýšlí...</span>
                </div>
            </div>
        `;

        this.chatMessages.appendChild(thinkingElement);
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    /**
     * Odstranění thinking zprávy z chatu
     */
    removeThinkingMessage() {
        const thinkingMessage = document.getElementById('thinking-message');
        if (thinkingMessage) {
            thinkingMessage.remove();
        }
    }

    /**
     * Přidání zprávy do chatu
     */
    addMessage(content, type = 'assistant') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        
        if (typeof content === 'string') {
            // Jednoduchý text
            contentDiv.innerHTML = this.formatMessage(content);
        } else if (content && typeof content === 'object') {
            // Strukturovaný obsah (např. finální analýza)
            contentDiv.innerHTML = this.formatStructuredContent(content);
        }
        
        messageDiv.appendChild(contentDiv);
        this.chatMessages.appendChild(messageDiv);
        
        // Scroll na konec
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    /**
     * Formátování textové zprávy
     */
    formatMessage(text) {
        return text
            .split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0)
            .map(line => `<p>${this.escapeHtml(line)}</p>`)
            .join('');
    }

    /**
     * Formátování strukturovaného obsahu
     */
    formatStructuredContent(content) {
        let html = '<div class="structured-content">';
        
        if (content.projekt_cil) {
            html += `<h3>🎯 Cíl projektu</h3><p>${this.escapeHtml(content.projekt_cil)}</p>`;
        }
        
        if (content.cilova_skupina) {
            html += `<h3>👥 Cílová skupina</h3><p>${this.escapeHtml(content.cilova_skupina)}</p>`;
        }
        
        if (content.klicove_otazky && Array.isArray(content.klicove_otazky)) {
            html += '<h3>❓ Klíčové otázky</h3><ul>';
            content.klicove_otazky.forEach(otazka => {
                html += `<li>${this.escapeHtml(otazka)}</li>`;
            });
            html += '</ul>';
        }
        
        if (content.doporuceni) {
            html += `<h3>💡 Doporučení</h3><p>${this.escapeHtml(content.doporuceni)}</p>`;
        }
        
        html += '</div>';
        return html;
    }

    /**
     * Escape HTML pro bezpečnost
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Odeslání zprávy
     */
    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message || this.isLoading) return;
        
        // Přidání uživatelské zprávy
        this.addMessage(message, 'user');
        
        // Vyčištění inputu
        this.messageInput.value = '';
        this.updateCharCounter();
        this.autoResizeTextarea();
        
        this.showLoading();
        
        try {
            const response = await this.callChatAPI(message);
            this.handleAPIResponse(response);
        } catch (error) {
            this.handleError(error);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Volání Chat API
     */
    async callChatAPI(message) {
        const requestBody = {
            message: message
        };

        // Přidání session_id pokud existuje
        if (this.sessionId) {
            requestBody.session_id = this.sessionId;
        }

        // Přidání explicitní domény pokud je vybrána
        const selectedDomain = this.domainSelect.value;
        if (selectedDomain) {
            requestBody.domain = selectedDomain;
        }

        // Přidání request_type pro lepší domain detection
        if (!selectedDomain) {
            // Jednoduchá detekce typu na základě klíčových slov
            const lowerMessage = message.toLowerCase();
            if (lowerMessage.includes('alfa') || lowerMessage.includes('alfa_tým') || lowerMessage.includes('výzkum_města') || lowerMessage.includes('veřejné_mínění') || lowerMessage.includes('městský_průzkum')) {
                requestBody.request_type = 'alfa';
            } else if (lowerMessage.includes('anketa') || lowerMessage.includes('průzkum') || lowerMessage.includes('výzkum')) {
                requestBody.request_type = 'průzkum';
            } else if (lowerMessage.includes('prodej') || lowerMessage.includes('klient') || lowerMessage.includes('zákazník')) {
                requestBody.request_type = 'sales';
            } else if (lowerMessage.includes('rozpočet') || lowerMessage.includes('participace')) {
                requestBody.request_type = 'rozpočet';
            } else if (lowerMessage.includes('podpora') || lowerMessage.includes('problém') || lowerMessage.includes('pomoc')) {
                requestBody.request_type = 'support';
            }
        }
        
        const response = await fetch(`${this.apiBaseUrl}/chat/universal`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return await response.json();
    }

    /**
     * Zpracování odpovědi z API
     */
    handleAPIResponse(response) {
        // Uložení session_id
        if (response.session_id) {
            this.sessionId = response.session_id;

            // Integrace s canvas managerem
            if (window.canvasManager) {
                window.canvasManager.setSessionId(this.sessionId);
            }
        }

        // Aktualizace domain badge pokud byla detekována doména
        if (response.domain && !this.domainSelect.value) {
            const domainNames = {
                'onboarding_pruzkumy': 'Onboarding Průzkumů',
                'research_public_opinion_municipal': 'Výzkumná Jednotka Alfa',
                'sales_qualification': 'Sales Qualification',
                'participativni_rozpocet': 'Participativní Rozpočet',
                'customer_support': 'Customer Support'
            };
            this.domainBadge.textContent = domainNames[response.domain] || response.domain;
        }

        // Zobrazení chat odpovědi
        if (response.chat_response) {
            this.addMessage(response.chat_response, 'assistant');
        } else if (response.question) {
            // Zpětná kompatibilita
            this.addMessage(response.question, 'assistant');
        } else if (response.final_analysis) {
            // Finální analýza
            this.addMessage('🎉 Analýza dokončena! Zde je shrnutí:', 'assistant');
            this.addMessage(response.final_analysis, 'assistant');
        } else if (response.message) {
            // Obecná zpráva
            this.addMessage(response.message, 'assistant');
        }

        // Aktualizace canvas obsahu - delegace na canvas manager
        if (response.canvas_content && window.canvasManager) {
            // Canvas manager se postará o aktualizaci
            console.log('📋 Canvas obsah delegován na Canvas Manager');
        }

        this.updateUI();
    }

    /**
     * Zpracování chyb
     */
    handleError(error) {
        console.error('Chyba při komunikaci s API:', error);
        
        let errorMessage = 'Omlouvám se, došlo k chybě při komunikaci se serverem.';
        
        if (error.message.includes('Failed to fetch')) {
            errorMessage = 'Nelze se připojit k serveru. Zkontrolujte, zda je backend spuštěn.';
        } else if (error.message.includes('HTTP 429')) {
            errorMessage = 'Server je momentálně přetížen. Zkuste to prosím za chvíli.';
        } else if (error.message.includes('HTTP 500')) {
            errorMessage = 'Došlo k chybě na serveru. Zkuste to prosím znovu.';
        }
        
        this.addMessage(`❌ ${errorMessage}`, 'assistant');
        
        // Aktualizace status indikátoru
        const statusDot = this.statusIndicator.querySelector('.status-dot');
        const statusText = this.statusIndicator.querySelector('.status-text');
        statusDot.className = 'status-dot error';
        statusText.textContent = 'Chyba';
        
        setTimeout(() => {
            statusDot.className = 'status-dot';
            statusText.textContent = 'Připraveno';
        }, 5000);
    }

    /**
     * Inicializace resizer funkcionality
     */
    initResizer() {
        const resizer = document.getElementById('resizer');
        const chatPanel = document.getElementById('chatPanel');
        const canvasPanel = document.getElementById('canvasPanel');

        if (!resizer || !chatPanel || !canvasPanel) return;

        let isResizing = false;

        resizer.addEventListener('mousedown', () => {
            isResizing = true;
            document.body.style.cursor = 'col-resize';
            document.body.style.userSelect = 'none';
        });

        document.addEventListener('mousemove', (e) => {
            if (!isResizing) return;

            const container = document.querySelector('.resizable-container');
            const containerRect = container.getBoundingClientRect();
            const newChatWidth = e.clientX - containerRect.left;
            const containerWidth = containerRect.width;

            // Omezení na 20% - 80%
            const minWidth = containerWidth * 0.2;
            const maxWidth = containerWidth * 0.8;

            if (newChatWidth >= minWidth && newChatWidth <= maxWidth) {
                chatPanel.style.flex = `0 0 ${newChatWidth}px`;
                canvasPanel.style.flex = `1`;
            }
        });

        document.addEventListener('mouseup', () => {
            isResizing = false;
            document.body.style.cursor = '';
            document.body.style.userSelect = '';
        });
    }

    /**
     * Načtení domain-specific help obsahu
     */
    loadDomainHelp(domain) {
        const helpContent = this.getDomainHelpContent(domain);
        this.canvasContent.innerHTML = helpContent;
    }

    /**
     * Získání help obsahu podle domény
     */
    getDomainHelpContent(domain) {
        const helpContents = {
            'onboarding_pruzkumy': `
                <div class="domain-help">
                    <h2>🔍 Onboarding Průzkumů</h2>
                    <p>Specializuji se na přípravu a plánování výzkumných projektů.</p>

                    <h3>Co umím:</h3>
                    <ul>
                        <li>📊 Návrh metodologie průzkumů</li>
                        <li>🎯 Definice cílových skupin</li>
                        <li>📝 Tvorba dotazníků a otázek</li>
                        <li>📈 Plánování analýz a reportingu</li>
                        <li>💰 Odhad rozpočtu a časového rámce</li>
                    </ul>

                    <h3>Příklady projektů:</h3>
                    <ul>
                        <li>Průzkum spokojenosti občanů</li>
                        <li>Analýza potřeb komunity</li>
                        <li>Hodnocení veřejných služeb</li>
                        <li>Výzkum dopravních návyků</li>
                    </ul>

                    <div class="help-tip">
                        💡 <strong>Tip:</strong> Začněte tím, že mi řeknete, jaký průzkum chcete udělat a proč.
                    </div>
                </div>
            `,
            'research_public_opinion_municipal': `
                <div class="domain-help">
                    <h2>🔬 Výzkumná Jednotka Alfa</h2>
                    <p>Specializovaná posádka pro transformaci klientských zadání na profesionální výzkumné projekty veřejného mínění.</p>

                    <h3>Co umím:</h3>
                    <ul>
                        <li>🎯 Analýza výzkumných požadavků a strategické plánování</li>
                        <li>📊 Návrh metodologie pro průzkumy veřejného mínění</li>
                        <li>🏙️ Specializace na městskou problematiku a sociologii</li>
                        <li>📝 Tvorba dotazníků s pokročilými technikami (Likertova škála, sémantický diferenciál)</li>
                        <li>⚙️ Export do Limesurvey formátu (.lss)</li>
                        <li>🔒 Zajištění GDPR compliance</li>
                        <li>📈 Vizualizace projektového postupu</li>
                    </ul>

                    <h3>Specializace:</h3>
                    <ul>
                        <li>Průzkumy spokojenosti občanů v městských částech</li>
                        <li>Analýza dopravních návyků a potřeb</li>
                        <li>Hodnocení veřejných služeb a infrastruktury</li>
                        <li>Výzkum bezpečnosti a životního prostředí</li>
                        <li>Demografické a urbanistické analýzy</li>
                    </ul>

                    <h3>Pracovní postup:</h3>
                    <ol>
                        <li><strong>Strategická analýza</strong> - Rozbor požadavků a návrh metodiky</li>
                        <li><strong>Klientská komunikace</strong> - Profesionální odpověď a doporučení</li>
                        <li><strong>Projektové plátno</strong> - Vizuální přehled postupu a stavu</li>
                    </ol>

                    <div class="help-tip">
                        💡 <strong>Tip:</strong> Popište mi výzkumný projekt nebo městskou problematiku, kterou chcete zkoumat.
                    </div>
                </div>
            `,
            'sales_qualification': `
                <div class="domain-help">
                    <h2>💼 Sales Qualification</h2>
                    <p>Pomohu vám kvalifikovat obchodní příležitosti a optimalizovat prodejní proces.</p>

                    <h3>Co umím:</h3>
                    <ul>
                        <li>🎯 Kvalifikace leadů a příležitostí</li>
                        <li>📋 Analýza potřeb klientů</li>
                        <li>💡 Návrh řešení na míru</li>
                        <li>📊 Competitive intelligence</li>
                        <li>💰 Pricing a value proposition</li>
                    </ul>

                    <h3>Proces kvalifikace:</h3>
                    <ol>
                        <li>Identifikace decision makera</li>
                        <li>Analýza business potřeb</li>
                        <li>Hodnocení rozpočtu a timeframe</li>
                        <li>Competitive landscape</li>
                        <li>Next steps a timeline</li>
                    </ol>

                    <div class="help-tip">
                        💡 <strong>Tip:</strong> Popište mi klienta a jeho situaci, abychom mohli začít.
                    </div>
                </div>
            `,
            'participativni_rozpocet': `
                <div class="domain-help">
                    <h2>🏛️ Participativní Rozpočet</h2>
                    <p>Specializuji se na facilitaci občanské participace a transparentní rozpočtování.</p>

                    <h3>Co umím:</h3>
                    <ul>
                        <li>🗳️ Návrh participativních procesů</li>
                        <li>📊 Metodiky hodnocení projektů</li>
                        <li>💰 Rozpočtové plánování</li>
                        <li>👥 Facilitace komunitních setkání</li>
                        <li>📈 Monitoring a evaluace</li>
                    </ul>

                    <h3>Fáze procesu:</h3>
                    <ol>
                        <li>Příprava a komunikace</li>
                        <li>Sběr návrhů od občanů</li>
                        <li>Hodnocení proveditelnosti</li>
                        <li>Hlasování a výběr</li>
                        <li>Implementace a reporting</li>
                    </ol>

                    <div class="help-tip">
                        💡 <strong>Tip:</strong> Řekněte mi o vaší komunitě a dostupném rozpočtu.
                    </div>
                </div>
            `,
            'customer_support': `
                <div class="domain-help">
                    <h2>🎧 Customer Support</h2>
                    <p>Pomohu vám optimalizovat zákaznickou podporu a řešit komplexní případy.</p>

                    <h3>Co umím:</h3>
                    <ul>
                        <li>🔧 Troubleshooting komplexních problémů</li>
                        <li>📚 Správa znalostní báze</li>
                        <li>📊 Analýza customer satisfaction</li>
                        <li>⚡ Optimalizace response time</li>
                        <li>🎯 Escalation management</li>
                    </ul>

                    <h3>Typy podpory:</h3>
                    <ul>
                        <li>Technická podpora</li>
                        <li>Billing a account management</li>
                        <li>Product support</li>
                        <li>Complaint resolution</li>
                    </ul>

                    <div class="help-tip">
                        💡 <strong>Tip:</strong> Popište mi problém nebo situaci, kterou potřebujete vyřešit.
                    </div>
                </div>
            `
        };

        return helpContents[domain] || `
            <div class="domain-help">
                <h2>🤖 Matylda v2.0</h2>
                <p>Vítejte v univerzální AI platformě pro strategické partnerství!</p>

                <h3>Dostupné domény:</h3>
                <ul>
                    <li>🔍 <strong>Onboarding Průzkumů</strong> - Výzkumné projekty</li>
                    <li>🔬 <strong>Výzkumná Jednotka Alfa</strong> - Specializované výzkumy veřejného mínění</li>
                    <li>💼 <strong>Sales Qualification</strong> - Obchodní příležitosti</li>
                    <li>🏛️ <strong>Participativní Rozpočet</strong> - Občanská participace</li>
                    <li>🎧 <strong>Customer Support</strong> - Zákaznická podpora</li>
                </ul>

                <div class="help-tip">
                    💡 <strong>Tip:</strong> Vyberte doménu výše nebo začněte psát a nechte mě automaticky detekovat správnou oblast.
                </div>
            </div>
        `;
    }
}

// Inicializace při načtení stránky
document.addEventListener('DOMContentLoaded', () => {
    window.matyldaChat = new MatyldaChat();
});
