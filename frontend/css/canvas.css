/**
 * Canvas Styles - Styly pro sdílené projektové plátno
 * Implementuje debug/production módy a real-time aktivity
 */

/* ============================================================================
   CANVAS INTEGRATION S EXISTUJÍCÍ STRUKTUROU
   ============================================================================ */

/* Integrace s existujícím canvas-panel */
.canvas-panel.debug-mode {
    border-color: #ff6b35;
    box-shadow: 0 4px 16px rgba(255, 107, 53, 0.2);
}

/* Rozšíření existujícího panel-header */
.panel-header {
    position: relative;
}

.canvas-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: auto;
}

/* ============================================================================
   DEBUG TOGGLE
   ============================================================================ */

.debug-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    user-select: none;
}

.debug-toggle input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 44px;
    height: 24px;
    background: #ccc;
    border-radius: 24px;
    transition: background 0.3s ease;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.debug-toggle input:checked + .toggle-slider {
    background: #ff6b35;
}

.debug-toggle input:checked + .toggle-slider::before {
    transform: translateX(20px);
}

.toggle-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.debug-toggle input:checked ~ .toggle-label {
    color: #ff6b35;
}

/* ============================================================================
   REFRESH BUTTON
   ============================================================================ */

.btn-refresh {
    background: #007bff;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s ease;
}

.btn-refresh:hover {
    background: #0056b3;
}

/* ============================================================================
   CANVAS CONTENT INTEGRATION
   ============================================================================ */

/* Rozšíření existujícího canvas-content */
.canvas-content {
    position: relative;
}

.canvas-error {
    background: #f8d7da;
    color: #721c24;
    padding: 12px 16px;
    border-radius: 6px;
    border: 1px solid #f5c6cb;
    margin: 10px;
}

/* ============================================================================
   CANVAS MARKDOWN RENDERING
   ============================================================================ */

.canvas-markdown {
    line-height: 1.6;
    color: #333;
}

.canvas-markdown h1 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
    margin-bottom: 20px;
}

.canvas-markdown h2 {
    color: #34495e;
    margin-top: 24px;
    margin-bottom: 12px;
}

.canvas-markdown h3 {
    color: #7f8c8d;
    margin-top: 20px;
    margin-bottom: 10px;
}

.canvas-markdown pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

/* ============================================================================
   DEBUG PANEL - INTEGRACE DO PRAVÉHO SLOUPCE
   ============================================================================ */

.debug-panel {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
    z-index: 10;
    display: flex;
    flex-direction: column;
}

.debug-tabs {
    display: flex;
    border-bottom: 1px solid #e1e5e9;
    background: #fff;
    border-radius: 8px 8px 0 0;
    flex-shrink: 0;
}

.debug-tab {
    background: none;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 12px;
    color: #666;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
    flex: 1;
    text-align: center;
}

.debug-tab:hover {
    background: #e9ecef;
    color: #333;
}

.debug-tab.active {
    color: #ff6b35;
    border-bottom-color: #ff6b35;
    background: #f8f9fa;
}

.debug-content {
    padding: 12px;
    flex: 1;
    overflow-y: auto;
}

.debug-tab-content {
    display: none;
    height: 100%;
}

.debug-tab-content.active {
    display: block;
}

/* ============================================================================
   AGENT ACTIVITIES - KOMPAKTNÍ VERZE
   ============================================================================ */

.activities-list {
    height: 100%;
    overflow-y: auto;
}

.no-activities {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 20px;
    font-size: 12px;
}

.activity-item {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    margin-bottom: 8px;
    padding: 8px 10px;
    transition: all 0.3s ease;
    font-size: 12px;
}

.activity-item:hover {
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.activity-item.completed {
    border-left: 3px solid #28a745;
}

.activity-item.in_progress {
    border-left: 3px solid #ffc107;
}

.activity-item.failed {
    border-left: 3px solid #dc3545;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.activity-agent {
    font-weight: 600;
    color: #2c3e50;
    font-size: 11px;
}

.activity-status {
    font-size: 14px;
}

.activity-time {
    font-size: 10px;
    color: #666;
}

.activity-description {
    color: #555;
    font-size: 11px;
    line-height: 1.3;
}

.activity-duration {
    margin-top: 4px;
    font-size: 10px;
    color: #666;
    font-style: italic;
}

/* ============================================================================
   DEBUG VIEW - KOMPAKTNÍ VERZE
   ============================================================================ */

.debug-text {
    background: #2d3748;
    color: #e2e8f0;
    border-radius: 4px;
    padding: 8px;
    font-family: 'Courier New', monospace;
    font-size: 10px;
    line-height: 1.3;
    height: 100%;
    overflow-y: auto;
    white-space: pre-wrap;
}

/* ============================================================================
   METADATA GRID - KOMPAKTNÍ VERZE
   ============================================================================ */

.metadata-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
}

.metadata-item {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    padding: 8px 10px;
}

.metadata-item label {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2px;
    font-size: 10px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.metadata-item span {
    color: #555;
    font-size: 11px;
}

/* ============================================================================
   RESPONSIVE DESIGN
   ============================================================================ */

@media (max-width: 768px) {
    .canvas-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .canvas-controls {
        justify-content: space-between;
    }
    
    .debug-tabs {
        flex-wrap: wrap;
    }
    
    .debug-tab {
        flex: 1;
        min-width: 120px;
    }
    
    .metadata-grid {
        grid-template-columns: 1fr;
    }
}

/* ============================================================================
   ANIMATIONS
   ============================================================================ */

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.activity-item.in_progress {
    animation: pulse 2s infinite;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.activity-item {
    animation: slideIn 0.3s ease;
}

/* ============================================================================
   ACTIVITY MESSAGES V CHATU
   ============================================================================ */

.message.activity-message {
    background: #f8f9fa;
    border-left: 3px solid #007bff;
    margin: 8px 0;
    opacity: 0.9;
}

.message.activity-message .message-content {
    padding: 12px 16px;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.activity-agent {
    display: flex;
    align-items: center;
    gap: 6px;
}

.agent-icon {
    font-size: 14px;
}

.agent-name {
    font-size: 12px;
    font-weight: 600;
    color: #2c3e50;
}

.activity-status {
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-icon {
    font-size: 12px;
}

.activity-time {
    font-size: 10px;
    color: #666;
}

.activity-description {
    font-size: 13px;
    color: #555;
    margin-bottom: 8px;
    line-height: 1.4;
}

.activity-progress {
    height: 2px;
    background: #e9ecef;
    border-radius: 1px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    border-radius: 1px;
    transition: all 0.3s ease;
}

.progress-bar.started {
    background: #007bff;
    width: 30%;
    animation: pulse 1.5s infinite;
}

.progress-bar.in_progress {
    background: #ffc107;
    width: 70%;
    animation: pulse 1.5s infinite;
}

.progress-bar.completed {
    background: #28a745;
    width: 100%;
}

.progress-bar.failed {
    background: #dc3545;
    width: 100%;
}

/* Animace pro dokončené aktivity */
.message.activity-message.completed {
    animation: fadeOut 3s ease-in-out 2s forwards;
}

@keyframes fadeOut {
    to {
        opacity: 0;
        transform: translateY(-10px);
        margin: 0;
        padding: 0;
        height: 0;
    }
}

/* ============================================================================
   ACTIVITY INDICATOR - CHATGPT REASONING STYLE (FALLBACK)
   ============================================================================ */

.activity-indicator {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    margin: 10px 0;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.activity-indicator.visible {
    opacity: 1;
    transform: translateY(0);
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid #e1e5e9;
    background: #fff;
    border-radius: 8px 8px 0 0;
}

.activity-title {
    font-size: 13px;
    font-weight: 600;
    color: #2c3e50;
}

.activity-toggle {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.activity-toggle:hover {
    background: #e9ecef;
}

.toggle-icon {
    font-size: 12px;
    color: #666;
}

.activity-list {
    max-height: 60px;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.activity-list.expanded {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item.completed {
    background: #f8fff8;
}

.activity-item.failed {
    background: #fff8f8;
}

.activity-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 4px;
}

.activity-agent {
    display: flex;
    align-items: center;
    gap: 6px;
    flex-shrink: 0;
}

.agent-icon {
    font-size: 14px;
}

.agent-name {
    font-size: 12px;
    font-weight: 600;
    color: #2c3e50;
}

.activity-description {
    flex: 1;
    font-size: 12px;
    color: #555;
    margin: 0 8px;
    line-height: 1.3;
}

.activity-status {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
}

.status-icon {
    font-size: 12px;
}

.activity-time {
    font-size: 10px;
    color: #666;
}

.activity-progress {
    height: 2px;
    background: #e9ecef;
    border-radius: 1px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    border-radius: 1px;
    transition: all 0.3s ease;
}

.progress-bar.started {
    background: #007bff;
    width: 30%;
    animation: pulse 1.5s infinite;
}

.progress-bar.in_progress {
    background: #ffc107;
    width: 70%;
    animation: pulse 1.5s infinite;
}

.progress-bar.completed {
    background: #28a745;
    width: 100%;
}

.progress-bar.failed {
    background: #dc3545;
    width: 100%;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

/* ============================================================================
   DARK MODE SUPPORT
   ============================================================================ */

@media (prefers-color-scheme: dark) {
    .canvas-panel {
        background: #1a1a1a;
        border-color: #333;
        color: #e0e0e0;
    }

    .panel-header {
        background: #2d2d2d;
        border-color: #333;
    }

    .panel-header h3 {
        color: #e0e0e0;
    }

    .debug-panel {
        background: #2d2d2d;
        border-color: #333;
    }

    .activity-item {
        background: #2d2d2d;
        border-color: #333;
        color: #e0e0e0;
    }

    .metadata-item {
        background: #2d2d2d;
        border-color: #333;
    }

    .activity-indicator {
        background: #2d2d2d;
        border-color: #333;
    }

    .activity-header {
        background: #1a1a1a;
        border-color: #333;
    }
}
