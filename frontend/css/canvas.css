/**
 * Canvas Styles - Styly pro sdílené projektové plátno
 * Implementuje debug/production módy a real-time aktivity
 */

/* ============================================================================
   CANVAS CONTAINER
   ============================================================================ */

.canvas-container {
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    margin: 20px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.canvas-container.debug-mode {
    border-color: #ff6b35;
    box-shadow: 0 4px 16px rgba(255, 107, 53, 0.2);
}

/* ============================================================================
   CANVAS HEADER
   ============================================================================ */

.canvas-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e1e5e9;
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
}

.canvas-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
}

.canvas-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* ============================================================================
   DEBUG TOGGLE
   ============================================================================ */

.debug-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    user-select: none;
}

.debug-toggle input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 44px;
    height: 24px;
    background: #ccc;
    border-radius: 24px;
    transition: background 0.3s ease;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.debug-toggle input:checked + .toggle-slider {
    background: #ff6b35;
}

.debug-toggle input:checked + .toggle-slider::before {
    transform: translateX(20px);
}

.toggle-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.debug-toggle input:checked ~ .toggle-label {
    color: #ff6b35;
}

/* ============================================================================
   REFRESH BUTTON
   ============================================================================ */

.btn-refresh {
    background: #007bff;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s ease;
}

.btn-refresh:hover {
    background: #0056b3;
}

/* ============================================================================
   CANVAS CONTENT
   ============================================================================ */

.canvas-content {
    display: flex;
    flex-direction: column;
}

.canvas-view {
    padding: 20px;
    min-height: 200px;
}

.canvas-placeholder {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 40px 20px;
}

.canvas-error {
    background: #f8d7da;
    color: #721c24;
    padding: 12px 16px;
    border-radius: 6px;
    border: 1px solid #f5c6cb;
}

/* ============================================================================
   CANVAS MARKDOWN RENDERING
   ============================================================================ */

.canvas-markdown {
    line-height: 1.6;
    color: #333;
}

.canvas-markdown h1 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
    margin-bottom: 20px;
}

.canvas-markdown h2 {
    color: #34495e;
    margin-top: 24px;
    margin-bottom: 12px;
}

.canvas-markdown h3 {
    color: #7f8c8d;
    margin-top: 20px;
    margin-bottom: 10px;
}

.canvas-markdown pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

/* ============================================================================
   DEBUG PANEL
   ============================================================================ */

.debug-panel {
    border-top: 1px solid #e1e5e9;
    background: #f8f9fa;
}

.debug-tabs {
    display: flex;
    border-bottom: 1px solid #e1e5e9;
}

.debug-tab {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.debug-tab:hover {
    background: #e9ecef;
    color: #333;
}

.debug-tab.active {
    color: #ff6b35;
    border-bottom-color: #ff6b35;
    background: white;
}

.debug-content {
    padding: 20px;
}

.debug-tab-content {
    display: none;
}

.debug-tab-content.active {
    display: block;
}

/* ============================================================================
   AGENT ACTIVITIES
   ============================================================================ */

.activities-list {
    max-height: 400px;
    overflow-y: auto;
}

.no-activities {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 20px;
}

.activity-item {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    margin-bottom: 12px;
    padding: 12px 16px;
    transition: all 0.3s ease;
}

.activity-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-item.completed {
    border-left: 4px solid #28a745;
}

.activity-item.in_progress {
    border-left: 4px solid #ffc107;
}

.activity-item.failed {
    border-left: 4px solid #dc3545;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.activity-agent {
    font-weight: 600;
    color: #2c3e50;
}

.activity-status {
    font-size: 16px;
}

.activity-time {
    font-size: 12px;
    color: #666;
}

.activity-description {
    color: #555;
    font-size: 14px;
    line-height: 1.4;
}

.activity-duration {
    margin-top: 8px;
    font-size: 12px;
    color: #666;
    font-style: italic;
}

/* ============================================================================
   DEBUG VIEW
   ============================================================================ */

.debug-text {
    background: #2d3748;
    color: #e2e8f0;
    border-radius: 6px;
    padding: 16px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    max-height: 500px;
    overflow-y: auto;
    white-space: pre-wrap;
}

/* ============================================================================
   METADATA GRID
   ============================================================================ */

.metadata-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.metadata-item {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    padding: 12px 16px;
}

.metadata-item label {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metadata-item span {
    color: #555;
    font-size: 14px;
}

/* ============================================================================
   RESPONSIVE DESIGN
   ============================================================================ */

@media (max-width: 768px) {
    .canvas-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .canvas-controls {
        justify-content: space-between;
    }
    
    .debug-tabs {
        flex-wrap: wrap;
    }
    
    .debug-tab {
        flex: 1;
        min-width: 120px;
    }
    
    .metadata-grid {
        grid-template-columns: 1fr;
    }
}

/* ============================================================================
   ANIMATIONS
   ============================================================================ */

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.activity-item.in_progress {
    animation: pulse 2s infinite;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.activity-item {
    animation: slideIn 0.3s ease;
}

/* ============================================================================
   DARK MODE SUPPORT
   ============================================================================ */

@media (prefers-color-scheme: dark) {
    .canvas-container {
        background: #1a1a1a;
        border-color: #333;
        color: #e0e0e0;
    }
    
    .canvas-header {
        background: #2d2d2d;
        border-color: #333;
    }
    
    .canvas-header h3 {
        color: #e0e0e0;
    }
    
    .debug-panel {
        background: #2d2d2d;
        border-color: #333;
    }
    
    .activity-item {
        background: #2d2d2d;
        border-color: #333;
        color: #e0e0e0;
    }
    
    .metadata-item {
        background: #2d2d2d;
        border-color: #333;
    }
}
