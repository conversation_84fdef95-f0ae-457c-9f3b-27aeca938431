/**
 * WebSocket Client - Real-time komunikace s backendem
 * Implementuje live updates pro canvas a agent aktivity
 */

class WebSocketClient {
    constructor() {
        this.socket = null;
        this.sessionId = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // 1 sekunda
        this.pingInterval = null;
        
        // Event listeners
        this.onCanvasUpdate = null;
        this.onActivitiesUpdate = null;
        this.onConnectionChange = null;
        
        this.init();
    }
    
    init() {
        console.log('🔌 WebSocket Client inicializován');
    }
    
    connect(sessionId) {
        if (this.isConnected && this.sessionId === sessionId) {
            console.log('🔌 WebSocket již připojen pro session:', sessionId);
            return;
        }
        
        this.sessionId = sessionId;
        this.disconnect(); // Odpojení p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ř<PERSON>
        
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.hostname;
            const port = '8001'; // Backend port
            const wsUrl = `${protocol}//${host}:${port}/ws/${sessionId}`;
            
            console.log(`🔌 Připojuji WebSocket: ${wsUrl}`);
            
            this.socket = new WebSocket(wsUrl);
            this.bindEvents();
            
        } catch (error) {
            console.error('❌ Chyba při vytváření WebSocket:', error);
            this.scheduleReconnect();
        }
    }
    
    disconnect() {
        if (this.socket) {
            console.log('🔌 Odpojuji WebSocket');
            this.socket.close();
            this.socket = null;
        }
        
        this.isConnected = false;
        this.clearPingInterval();
        
        if (this.onConnectionChange) {
            this.onConnectionChange(false);
        }
    }
    
    bindEvents() {
        if (!this.socket) return;
        
        this.socket.onopen = (event) => {
            console.log('✅ WebSocket připojen');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            
            this.startPingInterval();
            
            if (this.onConnectionChange) {
                this.onConnectionChange(true);
            }
        };
        
        this.socket.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleMessage(data);
            } catch (error) {
                console.error('❌ Chyba při parsování WebSocket zprávy:', error);
            }
        };
        
        this.socket.onclose = (event) => {
            console.log('🔌 WebSocket odpojen:', event.code, event.reason);
            this.isConnected = false;
            this.clearPingInterval();
            
            if (this.onConnectionChange) {
                this.onConnectionChange(false);
            }
            
            // Automatické přepojení pokud nebylo záměrné
            if (event.code !== 1000) {
                this.scheduleReconnect();
            }
        };
        
        this.socket.onerror = (error) => {
            console.error('❌ WebSocket chyba:', error);
        };
    }
    
    handleMessage(data) {
        console.log('📨 WebSocket zpráva:', data.type);
        
        switch (data.type) {
            case 'connection_established':
                console.log('✅ WebSocket připojení potvrzeno');
                break;
                
            case 'canvas_update':
                this.handleCanvasUpdate(data);
                break;
                
            case 'pong':
                // Odpověď na ping - connection je alive
                break;
                
            default:
                console.log('❓ Neznámý typ WebSocket zprávy:', data.type);
        }
    }
    
    handleCanvasUpdate(data) {
        console.log('🎨 Canvas update přes WebSocket');
        
        // Aktualizace canvas manageru
        if (window.canvasManager && this.onCanvasUpdate) {
            this.onCanvasUpdate(data.canvas_views);
        }
        
        // Aktualizace activity indicator
        if (window.activityIndicator && data.activities && this.onActivitiesUpdate) {
            this.onActivitiesUpdate(data.activities);
        }
    }
    
    startPingInterval() {
        this.clearPingInterval();
        
        // Ping každých 30 sekund pro keep-alive
        this.pingInterval = setInterval(() => {
            if (this.isConnected) {
                this.send({
                    type: 'ping',
                    timestamp: new Date().toISOString()
                });
            }
        }, 30000);
    }
    
    clearPingInterval() {
        if (this.pingInterval) {
            clearInterval(this.pingInterval);
            this.pingInterval = null;
        }
    }
    
    send(data) {
        if (this.socket && this.isConnected) {
            try {
                this.socket.send(JSON.stringify(data));
                return true;
            } catch (error) {
                console.error('❌ Chyba při odesílání WebSocket zprávy:', error);
                return false;
            }
        }
        return false;
    }
    
    requestUpdate() {
        return this.send({
            type: 'request_update',
            session_id: this.sessionId,
            timestamp: new Date().toISOString()
        });
    }
    
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('❌ Maximální počet pokusů o přepojení dosažen');
            return;
        }
        
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff
        
        console.log(`🔄 Pokus o přepojení ${this.reconnectAttempts}/${this.maxReconnectAttempts} za ${delay}ms`);
        
        setTimeout(() => {
            if (this.sessionId) {
                this.connect(this.sessionId);
            }
        }, delay);
    }
    
    // Event listener setters
    setOnCanvasUpdate(callback) {
        this.onCanvasUpdate = callback;
    }
    
    setOnActivitiesUpdate(callback) {
        this.onActivitiesUpdate = callback;
    }
    
    setOnConnectionChange(callback) {
        this.onConnectionChange = callback;
    }
    
    // Status getters
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            sessionId: this.sessionId,
            reconnectAttempts: this.reconnectAttempts
        };
    }
}

// Globální instance
window.webSocketClient = new WebSocketClient();

// Integrace s canvas managerem
if (window.canvasManager) {
    // Přepnutí canvas manageru na WebSocket updates
    window.webSocketClient.setOnCanvasUpdate((canvasViews) => {
        if (window.canvasManager) {
            // Aktualizace canvas bez HTTP requestu
            window.canvasManager.renderCanvas(canvasViews);
            
            if (canvasViews.metadata) {
                window.canvasManager.updateMetadata(canvasViews.metadata);
            }
        }
    });
    
    window.webSocketClient.setOnActivitiesUpdate((activities) => {
        if (window.activityIndicator) {
            window.activityIndicator.updateFromCanvasActivities(activities);
        }
    });
    
    window.webSocketClient.setOnConnectionChange((isConnected) => {
        // Aktualizace UI indikátoru připojení
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.querySelector('.status-text');
        const statusDot = document.querySelector('.status-dot');
        
        if (statusText && statusDot) {
            if (isConnected) {
                statusText.textContent = 'Připojeno (Live)';
                statusDot.style.backgroundColor = '#28a745';
            } else {
                statusText.textContent = 'Odpojeno';
                statusDot.style.backgroundColor = '#dc3545';
            }
        }
    });
}

// Auto-connect při nastavení session ID
const originalSetSessionId = window.canvasManager?.setSessionId;
if (originalSetSessionId) {
    window.canvasManager.setSessionId = function(sessionId) {
        // Volání původní metody
        originalSetSessionId.call(this, sessionId);
        
        // Připojení WebSocket
        window.webSocketClient.connect(sessionId);
        
        // Zastavení HTTP polling (WebSocket ho nahrazuje)
        this.stopAutoUpdates();
    };
}
