/**
 * Canvas Manager - Správa sdíleného projektového plátna
 * Implementuje debug/production toggle a real-time updates
 */

class CanvasManager {
    constructor() {
        this.sessionId = null;
        this.debugMode = false;
        this.currentCanvas = null;
        this.activities = [];
        this.updateInterval = null;
        
        // DOM elementy
        this.canvasContainer = null;
        this.debugToggle = null;
        this.activitiesContainer = null;
        
        this.init();
    }
    
    init() {
        console.log('🎨 Canvas Manager inicializován');
        this.createCanvasUI();
        this.bindEvents();
    }
    
    createCanvasUI() {
        // Použití existující struktury v pravém panelu
        const canvasPanel = document.getElementById('canvasPanel');
        const canvasContent = document.getElementById('canvasContent');

        if (!canvasPanel || !canvasContent) {
            console.error('❌ Canvas panel nenalezen v HTML struktuře');
            return;
        }

        // Přidání debug controls do panel-header
        const panelHeader = canvasPanel.querySelector('.panel-header');
        if (panelHeader && !panelHeader.querySelector('.canvas-controls')) {
            const controlsHTML = `
                <div class="canvas-controls">
                    <label class="debug-toggle">
                        <input type="checkbox" id="debug-mode-toggle">
                        <span class="toggle-slider"></span>
                        <span class="toggle-label">Debug</span>
                    </label>
                    <button id="refresh-canvas" class="btn-refresh">🔄</button>
                </div>
            `;
            panelHeader.insertAdjacentHTML('beforeend', controlsHTML);
        }

        // Přidání debug panelu do canvas-content
        if (!canvasContent.querySelector('#debug-panel')) {
            const debugPanelHTML = `
                <div id="debug-panel" class="debug-panel" style="display: none;">
                    <div class="debug-tabs">
                        <button class="debug-tab active" data-tab="activities">🤖 Activities</button>
                        <button class="debug-tab" data-tab="debug-view">🔧 Debug</button>
                        <button class="debug-tab" data-tab="metadata">📊 Metadata</button>
                    </div>

                    <div class="debug-content">
                        <div id="debug-activities" class="debug-tab-content active">
                            <div class="activities-list"></div>
                        </div>

                        <div id="debug-view-content" class="debug-tab-content">
                            <pre class="debug-text"></pre>
                        </div>

                        <div id="debug-metadata" class="debug-tab-content">
                            <div class="metadata-grid"></div>
                        </div>
                    </div>
                </div>
            `;
            canvasContent.insertAdjacentHTML('beforeend', debugPanelHTML);
        }

        // Získání referencí na DOM elementy
        this.canvasContainer = canvasPanel;
        this.debugToggle = document.getElementById('debug-mode-toggle');
        this.activitiesContainer = document.querySelector('.activities-list');
        this.canvasView = canvasContent;
        this.debugPanel = document.getElementById('debug-panel');
        this.canvasPlaceholder = document.getElementById('canvasPlaceholder');
    }
    
    bindEvents() {
        // Debug toggle
        if (this.debugToggle) {
            this.debugToggle.addEventListener('change', (e) => {
                this.toggleDebugMode(e.target.checked);
            });
        }
        
        // Refresh button
        const refreshBtn = document.getElementById('refresh-canvas');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshCanvas();
            });
        }
        
        // Debug tabs
        const debugTabs = document.querySelectorAll('.debug-tab');
        debugTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchDebugTab(e.target.dataset.tab);
            });
        });
    }
    
    setSessionId(sessionId) {
        this.sessionId = sessionId;
        console.log(`🆔 Canvas Manager: Session ID nastaven na ${sessionId}`);
        
        // Spuštění automatických updates
        this.startAutoUpdates();
    }
    
    async toggleDebugMode(enabled) {
        this.debugMode = enabled;
        console.log(`🔧 Debug mode: ${enabled ? 'ZAPNUT' : 'VYPNUT'}`);
        
        if (enabled) {
            this.debugPanel.style.display = 'block';
            this.canvasContainer.classList.add('debug-mode');
        } else {
            this.debugPanel.style.display = 'none';
            this.canvasContainer.classList.remove('debug-mode');
        }
        
        // Aktualizace canvas s novým módem
        await this.updateCanvas();
    }
    
    async updateCanvas() {
        if (!this.sessionId) {
            console.warn('⚠️ Canvas Manager: Žádné session ID');
            return;
        }
        
        try {
            const apiBaseUrl = window.frontendConfig?.getApiBaseUrl() || 'http://localhost:8001';
            const response = await fetch(`${apiBaseUrl}/canvas/${this.sessionId}?debug=${this.debugMode}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            this.currentCanvas = data;
            
            // Aktualizace UI
            this.renderCanvas(data);
            
            // Pokud je debug mode, aktualizuj i debug data
            if (this.debugMode) {
                await this.updateDebugData();
            }
            
        } catch (error) {
            console.error('❌ Chyba při aktualizaci canvas:', error);
            this.showCanvasError(error.message);
        }
    }
    
    renderCanvas(data) {
        if (!this.canvasView) return;

        if (this.debugMode && data.debug_view) {
            // Debug mode - skryj placeholder a zobraz debug view v debug panelu
            if (this.canvasPlaceholder) {
                this.canvasPlaceholder.style.display = 'none';
            }

            // Aktualizuj debug view tab
            const debugViewContent = document.querySelector('#debug-view-content .debug-text');
            if (debugViewContent) {
                debugViewContent.textContent = data.debug_view;
            }

        } else if (data.client_view) {
            // Production mode - zobraz client view místo placeholder
            if (this.canvasPlaceholder) {
                this.canvasPlaceholder.innerHTML = `
                    <div class="canvas-markdown">${this.markdownToHtml(data.client_view)}</div>
                `;
            }
        } else {
            // Žádná data - zobraz původní placeholder
            if (this.canvasPlaceholder) {
                this.canvasPlaceholder.innerHTML = `
                    <div class="placeholder-icon">📋</div>
                    <h3>Projektové Zadání</h3>
                    <p>⚠️ Canvas data nejsou dostupná</p>
                `;
            }
        }

        // Aktualizace metadata
        if (data.metadata) {
            this.updateMetadata(data.metadata);
        }
    }
    
    async updateDebugData() {
        if (!this.sessionId) return;

        try {
            // Získání agent activities
            const apiBaseUrl = window.frontendConfig?.getApiBaseUrl() || 'http://localhost:8001';
            const activitiesResponse = await fetch(`${apiBaseUrl}/canvas/${this.sessionId}/activities`);
            if (activitiesResponse.ok) {
                const activitiesData = await activitiesResponse.json();

                // Aktualizace debug panelu (pokud je debug mode)
                if (this.debugMode) {
                    this.renderActivities(activitiesData.activities);
                }

                // Aktualizace activity indicator (vždy)
                if (window.activityIndicator) {
                    window.activityIndicator.updateFromCanvasActivities(activitiesData.activities);
                }
            }

        } catch (error) {
            console.error('❌ Chyba při aktualizaci debug dat:', error);
        }
    }
    
    renderActivities(activities) {
        if (!this.activitiesContainer) return;
        
        if (!activities || activities.length === 0) {
            this.activitiesContainer.innerHTML = `
                <div class="no-activities">
                    ℹ️ Žádné aktivity agentů
                </div>
            `;
            return;
        }
        
        const activitiesHTML = activities.map(activity => {
            const statusIcon = this.getStatusIcon(activity.status);
            const typeIcon = this.getActivityTypeIcon(activity.activity_type);
            const timestamp = new Date(activity.timestamp).toLocaleTimeString();
            
            return `
                <div class="activity-item ${activity.status}">
                    <div class="activity-header">
                        <span class="activity-agent">${typeIcon} ${activity.agent_id}</span>
                        <span class="activity-status">${statusIcon}</span>
                        <span class="activity-time">${timestamp}</span>
                    </div>
                    <div class="activity-description">
                        ${activity.description}
                    </div>
                    ${activity.duration ? `<div class="activity-duration">⏱️ ${activity.duration}s</div>` : ''}
                </div>
            `;
        }).join('');
        
        this.activitiesContainer.innerHTML = activitiesHTML;
    }
    
    getStatusIcon(status) {
        const icons = {
            'started': '🚀',
            'in_progress': '⏳',
            'completed': '✅',
            'failed': '❌',
            'paused': '⏸️'
        };
        return icons[status] || '❓';
    }
    
    getActivityTypeIcon(type) {
        const icons = {
            'thinking': '🧠',
            'researching': '🔍',
            'writing': '✍️',
            'updating': '🔄',
            'reviewing': '👀',
            'waiting': '⏳'
        };
        return icons[type] || '🤖';
    }
    
    switchDebugTab(tabName) {
        // Deaktivace všech tabů
        document.querySelectorAll('.debug-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.debug-tab-content').forEach(content => {
            content.classList.remove('active');
        });
        
        // Aktivace vybraného tabu
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.getElementById(`debug-${tabName.replace('-', '-')}`).classList.add('active');
    }
    
    updateMetadata(metadata) {
        const metadataContainer = document.getElementById('debug-metadata');
        if (!metadataContainer) return;
        
        const metadataHTML = `
            <div class="metadata-grid">
                <div class="metadata-item">
                    <label>Projekt:</label>
                    <span>${metadata.project_name || 'N/A'}</span>
                </div>
                <div class="metadata-item">
                    <label>Fáze:</label>
                    <span>${metadata.current_phase || 'N/A'}</span>
                </div>
                <div class="metadata-item">
                    <label>Poslední aktualizace:</label>
                    <span>${metadata.last_updated ? new Date(metadata.last_updated).toLocaleString() : 'N/A'}</span>
                </div>
                <div class="metadata-item">
                    <label>Verze:</label>
                    <span>${metadata.total_versions || 0}</span>
                </div>
            </div>
        `;
        
        metadataContainer.innerHTML = metadataHTML;
    }
    
    startAutoUpdates() {
        // Zastavení předchozího intervalu
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }

        // Spuštění nového intervalu (každých 3 sekund pro rychlejší updates)
        this.updateInterval = setInterval(() => {
            this.updateCanvas();
            this.updateDebugData(); // Vždy aktualizuj aktivity pro activity indicator
        }, 3000);

        // Okamžitá aktualizace
        this.updateCanvas();
        this.updateDebugData();
    }
    
    stopAutoUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }
    
    async refreshCanvas() {
        console.log('🔄 Manuální refresh canvas');
        await this.updateCanvas();
    }
    
    showCanvasError(message) {
        this.canvasView.innerHTML = `
            <div class="canvas-error">
                ❌ Chyba při načítání canvas: ${message}
            </div>
        `;
    }
    
    // Utility funkce
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    markdownToHtml(markdown) {
        // Jednoduchá konverze Markdown na HTML
        return markdown
            .replace(/^### (.*$)/gim, '<h3>$1</h3>')
            .replace(/^## (.*$)/gim, '<h2>$1</h2>')
            .replace(/^# (.*$)/gim, '<h1>$1</h1>')
            .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
            .replace(/\*(.*)\*/gim, '<em>$1</em>')
            .replace(/\n/gim, '<br>');
    }
}

// Globální instance
window.canvasManager = new CanvasManager();
