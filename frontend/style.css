/* <PERSON><PERSON><PERSON> Frontend Styles - Moderní a profesionální design */

:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --background-color: #f8fafc;
    --surface-color: #ffffff;
    --border-color: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* App Container */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
}

/* Kompaktní hlavička */
.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    box-shadow: var(--shadow-sm);
    flex-shrink: 0;
}

.header-left .app-logo {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.domain-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.domain-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.domain-badge {
    padding: 0.25rem 0.75rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 9999px;
    font-size: 0.8rem;
    font-weight: 500;
}

.domain-select {
    padding: 0.25rem 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 0.8rem;
    cursor: pointer;
}

.domain-select option {
    background: var(--surface-color);
    color: var(--text-primary);
}

/* Main Content */
.main-content {
    flex: 1;
    overflow: hidden;
    padding: 0.5rem;
}

/* Resizable Container */
.resizable-container {
    display: flex;
    height: 100%;
    gap: 0;
}

/* Panel Headers */
.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-bottom: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    flex-shrink: 0;
}

.panel-header h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

/* Session Info */
.session-info {
    font-size: 0.8rem;
    color: var(--text-muted);
    background: var(--background-color);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
}

/* Progress Badge */
.progress-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.8rem;
    font-weight: 500;
    background: var(--success-color);
    color: white;
}

/* Chat Panel */
.chat-panel {
    display: flex;
    flex-direction: column;
    min-width: 300px;
    flex: 1;
}

/* Resizer */
.resizer {
    width: 4px;
    background: var(--border-color);
    cursor: col-resize;
    transition: background-color 0.2s ease;
    flex-shrink: 0;
}

.resizer:hover {
    background: var(--primary-color);
}

/* Canvas Panel */
.canvas-panel {
    display: flex;
    flex-direction: column;
    min-width: 300px;
    flex: 1;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-top: none;
    border-bottom: none;
    min-height: 0;
}

/* Messages */
.message {
    margin-bottom: 1.5rem;
    animation: fadeIn 0.3s ease-in;
}

.message-content {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 1.25rem;
    box-shadow: var(--shadow-sm);
    position: relative;
}

.system-message .message-content {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-color: #bae6fd;
}

.user-message .message-content {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border-color: #93c5fd;
    margin-left: 2rem;
}

.assistant-message .message-content {
    background: var(--surface-color);
    border-color: var(--border-color);
    margin-right: 2rem;
}

.message-content p {
    margin-bottom: 0.75rem;
}

.message-content p:last-child {
    margin-bottom: 0;
}

/* Chat Input */
.chat-input-container {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    padding: 1rem;
    flex-shrink: 0;
}

.input-group {
    display: flex;
    gap: 0.75rem;
    align-items: flex-end;
}

#messageInput {
    flex: 1;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    font-family: var(--font-family);
    font-size: 1rem;
    resize: none;
    min-height: 60px;
    max-height: 120px;
    transition: border-color 0.2s, box-shadow 0.2s;
}

#messageInput:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.send-button {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-height: 48px;
}

.send-button:hover:not(:disabled) {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.send-button:disabled {
    background: var(--text-muted);
    cursor: not-allowed;
    transform: none;
}

.input-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-muted);
}

/* Footer */
.footer {
    margin-top: 2rem;
    padding: 1rem 0;
    border-top: 1px solid var(--border-color);
}

.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
}

.status-dot.loading {
    background: var(--warning-color);
}

.status-dot.error {
    background: var(--error-color);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    background: var(--surface-color);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-lg);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 0.5rem;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .user-message .message-content {
        margin-left: 1rem;
    }
    
    .assistant-message .message-content {
        margin-right: 1rem;
    }
    
    .input-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .send-button {
        justify-content: center;
    }

    /* Responsive layout */
    .resizable-container {
        flex-direction: column;
    }

    .resizer {
        display: none;
    }

    .canvas-panel {
        order: -1;
        max-height: 40vh;
    }

    .app-header {
        padding: 0.5rem 1rem;
    }

    .header-right {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Canvas Styles */
.canvas-content {
    flex: 1;
    overflow-y: auto;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    min-height: 0;
}

.canvas-placeholder {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-muted);
}

.canvas-placeholder .placeholder-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.canvas-placeholder h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

.canvas-placeholder p {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

/* Canvas Content Styling */
.canvas-content .markdown-content {
    font-family: var(--font-family);
    line-height: 1.6;
}

.canvas-content .markdown-content h1,
.canvas-content .markdown-content h2,
.canvas-content .markdown-content h3 {
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    margin-top: 1.5rem;
}

.canvas-content .markdown-content h1:first-child,
.canvas-content .markdown-content h2:first-child,
.canvas-content .markdown-content h3:first-child {
    margin-top: 0;
}

.canvas-content .markdown-content p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

.canvas-content .markdown-content ul,
.canvas-content .markdown-content ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.canvas-content .markdown-content li {
    margin-bottom: 0.25rem;
    color: var(--text-secondary);
}

.canvas-content .markdown-content hr {
    border: none;
    border-top: 1px solid var(--border-color);
    margin: 2rem 0;
}

.canvas-content .markdown-content strong {
    color: var(--text-primary);
    font-weight: 600;
}

.canvas-content .markdown-content em {
    color: var(--text-muted);
    font-style: italic;
}

/* Domain Help Styles */
.domain-help {
    padding: 1rem;
    line-height: 1.6;
}

.domain-help h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.domain-help h3 {
    color: var(--text-primary);
    margin: 1.5rem 0 0.75rem 0;
    font-size: 1.1rem;
}

.domain-help ul, .domain-help ol {
    margin: 0.75rem 0;
    padding-left: 1.5rem;
}

.domain-help li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.help-tip {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-top: 1.5rem;
    border-left: 4px solid var(--primary-color);
}

.help-tip strong {
    color: var(--primary-color);
}

/* Send Button Disabled State */
.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: var(--text-muted);
}

.send-button:disabled:hover {
    background: var(--text-muted);
    transform: none;
}
